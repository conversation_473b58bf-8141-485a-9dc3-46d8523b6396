# 微机原理及单片机应用 - 学校知识点总结

## 📚 课程概览
- **课程名称**: 微机原理及单片机应用
- **考试时间**: 2025年7月2日10:00:00
- **复习资料来源**: 学校期末复习PDF（88页）
- **主要内容**: 8086微处理器 + 8051单片机

---

## 🔥 第一部分：微机原理（8086）

### 第一章 微型计算机概述
- **8086 CPU特点**:
  - 16位微处理器，40个引脚
  - 16位数据线，20位地址线
  - 直接寻址1MB存储空间（2^20bit = 1MB）
  - 内部操作16位，对外数据总线8位（8088）

### 第二章 16位微处理器8086

#### 1. CPU编程结构
- **总线接口部件BIU**:
  - 4个段地址寄存器：CS（代码段）、DS（数据段）、ES（附加段）、SS（堆栈段）
  - 指令指针寄存器IP
  - 6Byte指令队列缓冲器
  - 地址加法器：CS左移4位加上IP = 20位物理地址

- **执行部件EU**:
  - 通用寄存器：AX、BX、CX、DX
  - 专用寄存器：BP、SP、SI、DI
  - 算术逻辑部件ALU
  - 标志寄存器FR

#### 2. 标志寄存器（重点）
- **状态标志位**:
  - CF（进位标志）：最高位产生进位或借位
  - AF（辅助进位）：D3向D4进位或借位
  - ZF（零标志）：计算结果全零
  - PF（偶标志）：低八位中1的个数为偶数
  - SF（符号标志）：有符号数的符号位
  - OF（溢出标志）：有符号数溢出

- **控制标志位**:
  - IF（中断允许）：控制可屏蔽中断
  - TF（单步标志）：单步调试
  - DF（方向标志）：串操作方向

#### 3. 总线操作时序
- **读操作时序**：T1（地址）→ T2（数据准备）→ T3（数据传输）→ T4（结束）
- **写操作时序**：类似读操作，但数据方向相反

### 第三章 中断系统（考试重点）

#### 1. 中断基本概念
- **中断定义**：CPU暂停当前程序，转去执行中断服务程序
- **中断源分类**：
  - 硬件中断：非屏蔽中断（NMI）、可屏蔽中断（INTR）
  - 软件中断：INT指令

#### 2. 中断向量表（必考）
- **位置**：00000H~003FFH（1KB）
- **结构**：256个中断向量，每个4字节（IP低字 + CS高字）
- **计算公式**：中断类型号 × 4 = 中断向量起始地址

**填表和查表例题**：
```
中断类型码3H，入口地址1E00:0A00H
地址计算：3H × 4 = 0CH
填写：
0000:000CH = 00H (IP低字节)
0000:000DH = 0AH (IP高字节)  
0000:000EH = 00H (CS低字节)
0000:000FH = 1EH (CS高字节)
```

#### 3. 中断响应过程
1. 保存标志寄存器
2. IF和TF清零
3. 保存断点（CS、IP）
4. 查中断向量表
5. 转入中断服务程序

#### 4. 中断服务程序结构
```assembly
SER08 PROC FAR
    STI          ; 开中断（允许嵌套）
    ; 保存寄存器
    ; 中断处理
    ; 恢复寄存器
    CLI          ; 关中断
    ; 发送EOI命令给8259A
    IRET         ; 中断返回
SER08 ENDP
```

### 第四章 存储器编址
- **段式管理**：物理地址 = 段基址 × 16 + 偏移量
- **存储器组织**：分为奇偶两个存储体
- **寻址范围**：1MB（00000H~FFFFFH）

---

## 🔥 第二部分：接口技术

### 第五章 串行通信接口8251A

#### 1. 工作模式
- **异步模式**：起始位 + 数据位 + 校验位 + 停止位
- **同步模式**：同步字符 + 数据块

#### 2. 编程要点
- **初始化顺序**：
  - 异步：模式字 → 控制字
  - 同步：模式字 → 同步字符 → 控制字
- **端口地址**：偶地址传输数据，奇地址传输控制/状态

### 第六章 并行接口8255A（重点）

#### 1. 工作方式
- **方式0**：基本输入/输出，无握手信号
- **方式1**：选通输入/输出，有握手信号（重点）
- **方式2**：双向传输（仅A口）

#### 2. 方式1详解（考试重点）
**输入时序**：
1. 外设发送STB信号
2. IBF置1（输入缓冲器满）
3. 如果INTE=1，产生INTR中断
4. CPU读取数据，清除IBF和INTR

**输出时序**：
1. CPU写数据到端口
2. OBF置0（输出缓冲器满）
3. 外设发送ACK信号
4. 如果INTE=1，产生INTR中断

#### 3. 控制字设置
```
方式选择控制字格式：
D7=1, D6D5=方式选择, D4=A口方向, D3=C高方向
D2=B口方式, D1=B口方向, D0=C低方向
```

### 第七章 中断控制器8259A

#### 1. 初始化命令字
- **ICW1**（偶地址）：边沿/电平触发，单片/级联
- **ICW2**（奇地址）：中断类型号高5位
- **ICW4**（奇地址）：嵌套方式，缓冲方式，结束方式

#### 2. 操作命令字
- **OCW1**（奇地址）：中断屏蔽寄存器IMR
- **OCW2**（偶地址）：中断结束命令EOI
- **OCW3**（偶地址）：状态读取

#### 3. 典型初始化例程
```assembly
MOV AL, 13H    ; ICW1: 边沿触发，单片
OUT 20H, AL
MOV AL, 08H    ; ICW2: 中断类型08H-0FH  
OUT 21H, AL
MOV AL, 01H    ; ICW4: 普通嵌套，非自动EOI
OUT 21H, AL
```

### 第八章 定时器8253

#### 1. 工作模式
- **模式0**：计数结束中断
- **模式1**：硬件可重触发单稳
- **模式2**：分频器（重点）
- **模式3**：方波发生器（重点）

#### 2. 编程格式
```assembly
MOV AL, 36H    ; 控制字：通道0，模式3，二进制
OUT 43H, AL
MOV AL, 低字节
OUT 40H, AL
MOV AL, 高字节  
OUT 40H, AL
```

---

## 🔥 第三部分：单片机8051

### 第九章 8051单片机结构

#### 1. 基本特点
- **8位单片机**，40引脚
- **内部存储器**：128字节RAM + 4KB ROM
- **4个8位I/O口**：P0、P1、P2、P3
- **2个16位定时器**：T0、T1
- **1个串行口**
- **5个中断源**

#### 2. 存储器结构
- **程序存储器**：内部4KB + 外部64KB
- **数据存储器**：内部128B + 外部64KB
- **特殊功能寄存器SFR**：80H-FFH

#### 3. I/O端口特点
- **P0口**：开漏输出，需外接上拉电阻
- **P1口**：准双向口，内部上拉
- **P2口**：准双向口，外扩时作高8位地址
- **P3口**：准双向口，第二功能（串口、中断、定时器）

### 第十章 指令系统

#### 1. 寻址方式
- **立即寻址**：#data
- **直接寻址**：direct
- **寄存器寻址**：Rn
- **寄存器间接寻址**：@Ri
- **变址寻址**：A+DPTR, A+PC

#### 2. 指令分类
- **数据传送**：MOV, MOVC, MOVX
- **算术运算**：ADD, SUBB, MUL, DIV
- **逻辑运算**：ANL, ORL, XRL
- **控制转移**：JMP, CALL, RET
- **位操作**：SETB, CLR, CPL

### 第十一章 定时器/计数器

#### 1. 工作模式（重点）
- **模式0**：13位计数器
- **模式1**：16位计数器（常用）
- **模式2**：8位自动重装（常用）
- **模式3**：双8位计数器

#### 2. 控制寄存器
- **TMOD**：定时器模式控制
- **TCON**：定时器控制，包含TF0、TF1溢出标志

#### 3. 定时计算公式
```
定时时间 = (65536 - 初值) × 机器周期
机器周期 = 12 / 晶振频率
```

### 第十二章 串行口

#### 1. 工作模式
- **模式0**：同步移位寄存器（重点）
- **模式1**：8位异步通信
- **模式2**：9位异步通信
- **模式3**：9位异步通信

#### 2. 控制寄存器SCON
- **SM0、SM1**：模式选择
- **REN**：接收允许
- **TI**：发送中断标志
- **RI**：接收中断标志

### 第十三章 中断系统

#### 1. 中断源
- **外部中断0**：INT0（P3.2）
- **定时器0**：T0溢出
- **外部中断1**：INT1（P3.3）
- **定时器1**：T1溢出
- **串行口**：发送/接收完成

#### 2. 中断优先级
- **高优先级**：外部中断0、定时器0
- **低优先级**：外部中断1、定时器1、串行口

---

## 🔥 第四部分：应用系统设计

### 键盘接口
#### 1. 独立按键
- **硬件消抖**：RC电路
- **软件消抖**：延时程序

#### 2. 矩阵键盘
- **扫描法**：逐行扫描，检测列变化
- **编码**：行号×列数+列号

### LED显示
#### 1. 静态显示
- **直接驱动**：每个数码管独立控制
- **优点**：亮度高，编程简单
- **缺点**：占用I/O口多

#### 2. 动态显示
- **分时复用**：快速轮流显示
- **优点**：节省I/O口
- **缺点**：亮度稍低，编程复杂

---

## 📝 考试重点总结

### 必考知识点
1. **中断向量表的填表和查表**
2. **8255A方式1的工作时序**
3. **8259A的初始化编程**
4. **8051定时器的模式和计算**
5. **标志寄存器各位的含义**
6. **中断服务程序的编写**

### 计算题重点
1. **物理地址计算**：段基址×16+偏移量
2. **中断向量地址计算**：类型号×4
3. **定时器初值计算**：(最大值-初值)×周期
4. **8253分频计算**：输入频率/初值

### 编程题重点
1. **中断系统初始化**
2. **8255A接口编程**
3. **8259A中断控制**
4. **8051定时器应用**
5. **串行通信编程**

---

## 🎯 复习建议

### 时间分配（剩余约40小时）
1. **基础概念复习**（8小时）：CPU结构、寻址方式、指令系统
2. **中断系统专题**（12小时）：中断向量表、8259A、中断程序
3. **接口技术专题**（10小时）：8255A、8251A、8253
4. **单片机应用**（6小时）：8051结构、定时器、串口
5. **综合练习**（4小时）：历年真题、模拟考试

### 学习重点
1. **理解概念**：不要死记硬背，理解工作原理
2. **多做练习**：特别是计算题和编程题
3. **总结规律**：找出各种芯片初始化的共同点
4. **实际应用**：结合实际电路理解接口功能

---

*本总结基于学校提供的88页PDF复习资料整理，涵盖了微机原理及单片机应用的核心知识点。建议结合课本和习题进行深入学习。*
