# 微机原理及单片机应用 - 模拟试卷

## 📋 模拟试卷A（期末考试仿真版）

### 🎯 考试说明
- **考试时间**：120分钟
- **总分**：100分
- **题型分布**：选择题、填空题、计算题、编程题、简答题
- **答题要求**：请在规定时间内完成，模拟真实考试环境

### 📊 分值分布
| 题型 | 题数 | 每题分值 | 总分 |
|------|------|----------|------|
| 选择题 | 10题 | 2分 | 20分 |
| 填空题 | 10题 | 2分 | 20分 |
| 计算题 | 4题 | 8分 | 32分 |
| 编程题 | 2题 | 10分 | 20分 |
| 简答题 | 2题 | 4分 | 8分 |

---

## 📝 第一部分：选择题（每题2分，共20分）

**答题时间：20分钟**

### 1. 8086微处理器的地址总线位数是（）
A. 16位  B. 20位  C. 24位  D. 32位

### 2. 中断向量表在8086系统中占用的内存空间是（）
A. 256字节  B. 512字节  C. 1024字节  D. 2048字节

### 3. 8051单片机有（）个定时器/计数器
A. 1个  B. 2个  C. 3个  D. 4个

### 4. 8259A中断控制器可以管理（）个中断源
A. 4个  B. 6个  C. 8个  D. 16个

### 5. 8051定时器工作在模式2时的特点是（）
A. 13位计数  B. 16位计数  C. 8位自动重装  D. 双8位计数

### 6. 8255A并行接口芯片有（）种工作方式
A. 2种  B. 3种  C. 4种  D. 5种

### 7. 物理地址的计算公式是（）
A. 段地址 + 偏移地址  
B. 段地址 × 16 + 偏移地址
C. 段地址 × 10 + 偏移地址  
D. 段地址 ÷ 16 + 偏移地址

### 8. 8051串行口工作在模式1时，数据帧格式是（）
A. 8位数据  B. 9位数据  C. 10位数据  D. 11位数据

### 9. 8086的标志寄存器中，CF表示（）
A. 零标志  B. 进位标志  C. 符号标志  D. 溢出标志

### 10. 8051单片机的P0口的特点是（）
A. 准双向口  B. 开漏输出  C. 推挽输出  D. 高阻输入

---

## 📝 第二部分：填空题（每空2分，共20分）

**答题时间：20分钟**

### 1. 8086采用_____位地址总线，_____位数据总线，最大寻址空间为_____。

### 2. 中断向量表位于内存的_____地址到_____地址，共有_____个中断向量。

### 3. 8051单片机有_____个8位I/O口，分别是_____、_____、_____、_____。

### 4. 8259A的初始化需要_____个ICW，分别是_____、_____、_____、_____。

### 5. 8051定时器的机器周期计算公式是：机器周期 = _____ ÷ _____。

---

## 📝 第三部分：计算题（每题8分，共32分）

**答题时间：40分钟**

### 1. 中断向量表计算（8分）
已知中断类型号为1AH的中断服务程序入口地址为3000:2000H，请：
(1) 计算该中断向量在中断向量表中的存放地址（2分）
(2) 写出中断向量表中的具体内容（4分）
(3) 如果要查找中断类型号为25H的中断向量，应该访问哪个地址？（2分）

### 2. 物理地址计算（8分）
某程序运行时的寄存器状态如下：
CS=2000H, IP=1500H, DS=3000H, SS=4000H, SP=2000H
请计算：
(1) 当前指令的物理地址（2分）
(2) 数据段的起始物理地址（2分）
(3) 栈顶的物理地址（2分）
(4) 如果执行指令MOV AX, [1000H]，访问的物理地址是多少？（2分）

### 3. 8051定时器计算（8分）
使用8051定时器T0产生5ms定时中断，晶振频率为11.0592MHz，定时器工作在模式1。
请计算：
(1) 机器周期是多少？（2分）
(2) 5ms需要多少个机器周期？（2分）
(3) 定时器的初值是多少？（2分）
(4) TH0和TL0应该设置为什么值？（2分）

### 4. 8259A初始化计算（8分）
某系统使用8259A管理中断，要求：
- 边沿触发方式
- 单片工作
- 中断类型号为30H-37H
- 普通嵌套，非缓冲，手动EOI
- 端口地址为40H和41H

请写出：
(1) ICW1的值和含义（2分）
(2) ICW2的值和含义（2分）
(3) ICW4的值和含义（2分）
(4) 完整的初始化程序（2分）

---

## 📝 第四部分：编程题（每题10分，共20分）

**答题时间：30分钟**

### 1. 8051定时器编程（10分）
编写一个程序，使用定时器T0产生1秒定时，每秒钟让P1.0引脚的LED闪烁一次（亮500ms，灭500ms）。晶振频率12MHz。

要求：
(1) 写出定时器初始化程序（3分）
(2) 写出中断服务程序（4分）
(3) 写出主程序（3分）

### 2. 8255A接口编程（10分）
某系统使用8255A作为并行接口，端口地址为60H-63H。要求：
- 端口A工作在方式1输入
- 端口B工作在方式0输出
- 端口C高4位配合端口A工作，低4位为输出

请：
(1) 计算控制字的值（4分）
(2) 写出初始化程序（3分）
(3) 写出从端口A读取数据并输出到端口B的程序（3分）

---

## 📝 第五部分：简答题（每题4分，共8分）

**答题时间：10分钟**

### 1. 简述8086中断响应的完整过程（4分）

### 2. 说明8051单片机各I/O口的特点和用途（4分）

---

## 📋 模拟试卷A参考答案

### 第一部分：选择题答案
1. B  2. C  3. B  4. C  5. C  6. B  7. B  8. C  9. B  10. B

### 第二部分：填空题答案
1. 20，16，1MB
2. 00000H，003FFH，256
3. 4，P0，P1，P2，P3
4. 4，ICW1，ICW2，ICW3，ICW4（注：单片模式不需要ICW3）
5. 12，晶振频率

### 第三部分：计算题答案

#### 1. 中断向量表计算
(1) 存放地址 = 1AH × 4 = 68H，即0000:0068H-0000:006BH
(2) 中断向量表内容：
- 0000:0068H = 00H (IP低字节)
- 0000:0069H = 20H (IP高字节)
- 0000:006AH = 00H (CS低字节)
- 0000:006BH = 30H (CS高字节)
(3) 25H中断向量地址 = 25H × 4 = 94H，即0000:0094H

#### 2. 物理地址计算
(1) 当前指令地址 = 2000H × 16 + 1500H = 21500H
(2) 数据段起始地址 = 3000H × 16 = 30000H
(3) 栈顶地址 = 4000H × 16 + 2000H = 42000H
(4) MOV AX, [1000H]访问地址 = 3000H × 16 + 1000H = 31000H

#### 3. 8051定时器计算
(1) 机器周期 = 12 ÷ 11.0592MHz ≈ 1.085μs
(2) 5ms需要的机器周期 = 5ms ÷ 1.085μs ≈ 4608个
(3) 初值 = 65536 - 4608 = 60928 = EE00H
(4) TH0 = EEH，TL0 = 00H

#### 4. 8259A初始化
(1) ICW1 = 13H (边沿触发，单片，需要ICW4)
(2) ICW2 = 30H (中断类型30H-37H)
(3) ICW4 = 01H (普通嵌套，非缓冲，手动EOI)
(4) 初始化程序：
```assembly
MOV AL, 13H
OUT 40H, AL
MOV AL, 30H
OUT 41H, AL
MOV AL, 01H
OUT 41H, AL
```

### 第四部分：编程题答案

#### 1. 8051定时器编程
```c
#include <reg52.h>

sbit LED = P1^0;
unsigned char timer_count = 0;

void Timer0_Init() {
    TMOD |= 0x01;      // T0模式1
    TH0 = 0x3C;        // 50ms定时
    TL0 = 0xB0;
    TR0 = 1;
    ET0 = 1;
    EA = 1;
}

void Timer0_ISR() interrupt 1 {
    TH0 = 0x3C;
    TL0 = 0xB0;
    timer_count++;
    if(timer_count >= 10) {    // 500ms
        timer_count = 0;
        LED = ~LED;            // LED翻转
    }
}

void main() {
    Timer0_Init();
    while(1);
}
```

#### 2. 8255A接口编程
```assembly
; 控制字计算：10111000B = B8H
MOV AL, B8H
OUT 63H, AL

; 从端口A读取数据输出到端口B
INPUT_LOOP:
    IN AL, 60H         ; 读取端口A
    OUT 61H, AL        ; 输出到端口B
    JMP INPUT_LOOP
```

### 第五部分：简答题答案

#### 1. 8086中断响应过程
(1) 保存标志寄存器FR到栈中
(2) 清除IF和TF标志位
(3) 保存断点地址（CS和IP）到栈中
(4) 根据中断类型号查找中断向量表
(5) 将中断向量装入CS和IP寄存器
(6) 转入中断服务程序执行

#### 2. 8051各I/O口特点
- P0口：开漏输出，需外接上拉电阻，外扩时作低8位地址/数据复用
- P1口：准双向口，内部有上拉电阻，通用I/O口
- P2口：准双向口，外扩时作高8位地址线
- P3口：准双向口，具有第二功能（串口、中断、定时器控制）

---

## 📋 模拟试卷B（强化训练版）

### 🎯 考试说明
- **考试时间**：120分钟
- **总分**：100分
- **重点**：加强编程题和综合应用题的训练
- **难度**：比试卷A略高，重点考查理解和应用能力

### 📊 分值分布
| 题型 | 题数 | 每题分值 | 总分 |
|------|------|----------|------|
| 选择题 | 8题 | 2分 | 16分 |
| 填空题 | 8题 | 2分 | 16分 |
| 计算题 | 3题 | 10分 | 30分 |
| 编程题 | 3题 | 10分 | 30分 |
| 综合题 | 1题 | 8分 | 8分 |

---

## 📝 第一部分：选择题（每题2分，共16分）

### 1. 8086系统中，段地址1000H对应的段范围是（）
A. 10000H-1FFFFH  B. 1000H-1FFFH  C. 10000H-10FFFH  D. 1000H-10FFH

### 2. 8051定时器T0工作在模式1时，最大计数值是（）
A. 255  B. 8191  C. 65535  D. 131071

### 3. 8259A级联时，从片的ICW3设置为（）
A. 主片IR号  B. 从片级联号  C. 中断类型号  D. 端口地址

### 4. 8255A工作在方式1时，端口C的作用是（）
A. 数据传输  B. 地址选择  C. 握手信号  D. 控制命令

### 5. 8051串行口工作在模式0时的特点是（）
A. 异步通信  B. 同步移位  C. 9位数据  D. 双工通信

### 6. 中断向量表中每个中断向量的存储顺序是（）
A. CS高字节，CS低字节，IP高字节，IP低字节
B. IP低字节，IP高字节，CS低字节，CS高字节
C. IP高字节，IP低字节，CS高字节，CS低字节
D. CS低字节，CS高字节，IP低字节，IP高字节

### 7. 8051单片机复位后，PC的初值是（）
A. 0000H  B. 0003H  C. 0023H  D. FFFFH

### 8. 8086的标志寄存器中，OF标志表示（）
A. 无符号数溢出  B. 有符号数溢出  C. 进位标志  D. 零标志

---

## 📝 第二部分：填空题（每空2分，共16分）

### 1. 8086的BIU包含_____个段寄存器，分别是_____、_____、_____、_____。

### 2. 8259A的OCW1用于设置_____，OCW2用于_____，OCW3用于_____。

### 3. 8051定时器的TMOD寄存器中，GATE位的作用是_____，C/T位的作用是_____。

### 4. 8255A的端口C可以按位设置，置位命令的格式是_____，复位命令的格式是_____。

---

## 📝 第三部分：计算题（每题10分，共30分）

### 1. 综合地址计算（10分）
某程序段如下：
```assembly
MOV AX, 2000H
MOV DS, AX
MOV SI, 1000H
MOV BX, [SI+500H]
```
假设执行前CS=1000H，IP=2000H，执行后要访问的数据地址是多少？请详细计算过程。

### 2. 8259A级联系统（10分）
设计一个双片8259A级联系统：
- 主片端口地址：20H/21H，中断类型08H-0FH
- 从片端口地址：A0H/A1H，中断类型70H-77H
- 从片连接到主片IR2
- 要求屏蔽主片IR1和从片IR3中断

写出完整的初始化和屏蔽设置程序。

### 3. 8051复杂定时应用（10分）
使用8051设计一个交通灯控制系统：
- 红灯30秒，绿灯25秒，黄灯5秒
- 使用定时器T0产生1秒时基
- 晶振12MHz，定时器工作在模式2

计算定时器初值并编写控制程序框架。

---

## 📝 第四部分：编程题（每题10分，共30分）

### 1. 矩阵键盘扫描（10分）
编写4×4矩阵键盘扫描程序，键盘连接到P1口（P1.0-P1.3为行线，P1.4-P1.7为列线）。要求：
- 实现按键扫描功能
- 返回按键编号（1-16）
- 包含软件消抖处理

### 2. 数码管动态显示（10分）
编写8位数码管动态显示程序，显示数字"12345678"。要求：
- P0口接段选信号
- P2口接位选信号
- 使用定时器T0控制扫描频率
- 扫描频率约1KHz

### 3. 串行通信程序（10分）
编写8051串行通信程序，实现与PC机的数据传输。要求：
- 工作模式1，波特率9600bps
- 晶振11.0592MHz
- 能够发送和接收数据
- 包含中断处理程序

---

## 📝 第五部分：综合题（8分）

### 1. 微机系统设计（8分）
设计一个基于8086的数据采集系统：
- 使用8255A作为数据输入接口
- 使用8259A管理中断
- 采集到的数据存储在内存中
- 系统框图和主要程序设计思路

要求：
(1) 画出系统连接框图（2分）
(2) 说明各芯片的作用和连接方式（3分）
(3) 编写数据采集的主要程序流程（3分）

---

## 📊 考试技巧与时间分配建议

### ⏰ 时间分配策略
| 题型 | 建议时间 | 答题要点 |
|------|----------|----------|
| 选择题 | 15-20分钟 | 快速判断，不要纠结 |
| 填空题 | 15-20分钟 | 准确记忆，注意细节 |
| 计算题 | 35-40分钟 | 步骤清晰，公式正确 |
| 编程题 | 35-40分钟 | 逻辑清楚，语法规范 |
| 简答/综合题 | 10-15分钟 | 要点全面，表达清晰 |

### 📝 答题技巧
1. **选择题技巧**：
   - 排除法：先排除明显错误的选项
   - 代入法：将选项代入题目验证
   - 估算法：对于计算题可以估算数量级

2. **计算题技巧**：
   - 写出计算公式
   - 分步骤计算
   - 检查单位和进制
   - 验证结果合理性

3. **编程题技巧**：
   - 先理解需求
   - 设计算法流程
   - 注意语法规范
   - 添加必要注释

4. **时间管理**：
   - 先做会做的题目
   - 控制每题用时
   - 预留检查时间
   - 不要在难题上耗费过多时间

### 🎯 考前准备建议
1. **知识点梳理**：
   - 复习重点公式
   - 整理常见题型
   - 准备速查表

2. **模拟练习**：
   - 严格按时间要求
   - 模拟考试环境
   - 分析错题原因

3. **心理准备**：
   - 保持良好心态
   - 充足睡眠
   - 适度紧张有利发挥

---

*模拟试卷设计完成，包含两套完整的模拟试卷（A卷和B卷），涵盖了微机原理及单片机应用的所有重要知识点。试卷难度递进，既有基础题也有综合应用题，能够全面检验学生的学习效果。*
