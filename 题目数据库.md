# 微机原理及单片机应用 - 考试题目数据库

## 📚 题目来源统计
- **收集题目总数**: 150+ 道
- **覆盖知识点**: 微机原理（8086）+ 单片机应用（8051）+ 接口技术
- **题型分布**: 选择题、填空题、计算题、简答题、编程题
- **难度层次**: 基础、中等、提高

---

## 🔥 第一部分：微机原理（8086）考试题目

### 1. 中断系统（高频考点）

#### 1.1 中断向量表计算题（必考）
**例题1**：中断类型号为32H的中断向量存放在内存哪里？如果32H的中断处理子程序从13A4H:25B0H开始，中断向量应怎样存放？

**解答**：
- 中断向量存放地址 = 中断类型号 × 4 = 32H × 4 = C8H
- 段地址：0000H，偏移地址：00C8H-00CBH
- 存放内容：
  - 地址00C8H：B0H（IP低字节）
  - 地址00C9H：25H（IP高字节）
  - 地址00CAH：A4H（CS低字节）
  - 地址00CBH：13H（CS高字节）

**例题2**：若中断向量号分别为1AH和20H，则它们的中断向量在中断向量表的什么位置上？

**解答**：
- 1AH的位置：1AH × 4 = 68H，在0000:0068处
- 20H的位置：20H × 4 = 80H，在0000:0080处

#### 1.2 中断优先级和8259A编程
**例题3**：试按照如下要求对8259A进行初始化：系统中只有一片8259A，中断请求信号用电平触发方式，要用ICW4，中断类型码为60H-67H，用全嵌套方式，不用缓冲方式，采用中断自动结束方式。设8259A的端口地址为94H和95H。

**解答**：
```assembly
MOV DX, 94H     ; 偶地址
MOV AL, 13H     ; ICW1: 边沿触发，单片，要用ICW4
OUT DX, AL
MOV DX, 95H     ; 奇地址
MOV AL, 60H     ; ICW2: 中断类型60H-67H
OUT DX, AL
MOV AL, 01H     ; ICW4: 全嵌套，非缓冲，自动EOI
OUT DX, AL
```

### 2. 物理地址计算（高频考点）

#### 2.1 段地址和偏移地址计算
**例题4**：已知一个数据的段地址为224AH，偏移地址为5678H，求其物理地址？

**解答**：
- 物理地址 = 段地址 × 16 + 偏移地址
- = 224AH × 16 + 5678H
- = 224A0H + 5678H = 27B18H

**例题5**：设存储器的段地址是4ABFH，物理地址为50000H，其偏移地址为多少？

**解答**：
- 偏移地址 = 物理地址 - 段地址 × 16
- = 50000H - 4ABFH × 16
- = 50000H - 4ABF0H = 5410H

### 3. 标志寄存器和指令执行

#### 3.1 标志位计算
**例题6**：已知BX=7830H，CF=1，执行指令ADC BX，87CFH之后，BX和标志位的状态？

**解答**：
- BX = 7830H + 87CFH + 1(CF) = 10000H
- 结果：BX = 0000H，CF = 1，ZF = 1，OF = 0，SF = 0

---

## 🔥 第二部分：接口技术考试题目

### 1. 8255A并行接口（重点）

#### 1.1 方式控制字设置
**例题7**：对8255A进行初始化，要求端口A工作于方式1输入，端口B工作于方式0输出，端口C高4位配合端口A工作，低4位为输入。设控制口地址为006CH。

**解答**：
- 控制字：10111001B = B9H
```assembly
MOV AL, B9H
MOV DX, 006CH
OUT DX, AL
```

#### 1.2 端口C置位/复位控制
**例题8**：设8255A的端口地址分别为00C0H-00C3H，要求用置0、置1的方法对PC6置1，对PC4置0。

**解答**：
```assembly
MOV DX, 00C3H
MOV AL, 00001101B    ; 对PC6置1
OUT DX, AL
MOV AL, 00001000B    ; 对PC4置0
OUT DX, AL
```

### 2. 8253定时器/计数器

#### 2.1 定时器初始化编程
**例题9**：试编程对8253初始化。要求计数器0工作于模式1，初值为3000H；计数器1工作于模式3，初值为100H；计数器2工作于模式4，初值为4030H。设端口地址为40H-43H。

**解答**：
```assembly
; 计数器0初始化
MOV AL, 00110010B    ; 控制字
OUT 43H, AL
MOV AX, 3000H
OUT 40H, AL          ; 低字节
MOV AL, AH
OUT 40H, AL          ; 高字节

; 计数器1初始化
MOV AL, 01110110B
OUT 43H, AL
MOV AL, 00H
OUT 41H, AL
MOV AL, 01H
OUT 41H, AL

; 计数器2初始化
MOV AL, 10111000B
OUT 43H, AL
MOV AX, 4030H
OUT 42H, AL
MOV AL, AH
OUT 42H, AL
```

#### 2.2 频率计算
**例题10**：某系统的8251采用异步通讯方式，模式字为FBH，波特率为1100bps，求每秒输出多少字符，输出时钟频率是多少？

**解答**：
- FBH = 11111011B
- 数据帧长度：1起始位 + 7数据位 + 1校验位 + 2停止位 = 11位
- 每秒输出字符 = 1100/11 = 100个
- 输出时钟频率 = 波特率因子 × 波特率 = 64 × 1100 = 70400Hz

---

## 🔥 第三部分：8051单片机考试题目

### 1. 单片机基础结构

#### 1.1 存储器结构和寻址
**例题11**：8051单片机的特点和基本结构？

**解答**：
- 8位单片机，40引脚
- 内部存储器：128字节RAM + 4KB ROM
- 4个8位I/O口：P0、P1、P2、P3
- 2个16位定时器：T0、T1
- 1个串行口，5个中断源

#### 1.2 I/O端口特点
**例题12**：8051各端口的特点？

**解答**：
- P0口：开漏输出，需外接上拉电阻，外扩时作低8位地址/数据复用
- P1口：准双向口，内部上拉
- P2口：准双向口，外扩时作高8位地址
- P3口：准双向口，第二功能（串口、中断、定时器）

### 2. 定时器/计数器编程

#### 2.1 定时器模式和计算
**例题13**：使用定时器T0产生1ms的定时，晶振频率12MHz，求初值？

**解答**：
- 机器周期 = 12/12MHz = 1μs
- 定时1ms需要1000个机器周期
- 初值 = 65536 - 1000 = 64536 = FC18H
- TH0 = FCH，TL0 = 18H

#### 2.2 PWM波形生成
**例题14**：使用单片机产生10KHz频率，占空比25%和75%交替变化的PWM波形？

**解答**：
```c
// 定时器0初始化，100μs中断一次
void Timer0_Init() {
    TMOD |= 0x01;        // 模式1
    TH0 = 0xFF;
    TL0 = 0xA4;          // 100μs
    TR0 = 1;
    ET0 = 1;
    EA = 1;
}

// 中断服务程序
void Timer0_ISR() interrupt 1 {
    TH0 = 0xFF;
    TL0 = 0xA4;
    Counter++;
    Counter %= 100;      // 10KHz = 100μs周期
    
    if(Counter < Compare) {
        PWM_PIN = 1;
    } else {
        PWM_PIN = 0;
    }
}

// 主程序
void main() {
    Timer0_Init();
    while(1) {
        Compare = 25;    // 25%占空比
        Delay(1000);
        Compare = 75;    // 75%占空比
        Delay(1000);
    }
}
```

### 3. 串行通信编程

#### 3.1 串口初始化
**例题15**：8051串口工作模式和初始化？

**解答**：
- 模式0：同步移位寄存器
- 模式1：8位异步通信（常用）
- 模式2：9位异步通信
- 模式3：9位异步通信

```c
// 串口初始化，9600bps@11.0592MHz
void UART_Init() {
    SCON = 0x50;         // 模式1，允许接收
    TMOD |= 0x20;        // 定时器1模式2
    TH1 = 0xFD;          // 9600bps
    TL1 = 0xFD;
    TR1 = 1;             // 启动定时器1
}
```

### 4. 中断系统编程

#### 4.1 中断优先级和编程
**例题16**：8051中断系统的特点和编程？

**解答**：
- 5个中断源：外部中断0、定时器0、外部中断1、定时器1、串行口
- 2级优先级：高优先级可以中断低优先级
- 中断允许控制：EA（总开关）、各中断源开关

```c
// 中断初始化
void Interrupt_Init() {
    EA = 1;              // 总中断开关
    EX0 = 1;             // 外部中断0允许
    ET0 = 1;             // 定时器0中断允许
    IT0 = 1;             // 外部中断0下降沿触发
}

// 外部中断0服务程序
void EXT0_ISR() interrupt 0 {
    // 中断处理代码
}
```

---

## 🔥 第四部分：综合应用题目

### 1. 矩阵键盘扫描

#### 1.1 4×4矩阵键盘
**例题17**：编写4×4矩阵键盘扫描程序？

**解答**：
```c
unsigned char MatrixKey() {
    unsigned char KeyNumber = 0;
    
    // 扫描第1行
    P1 = 0xFF;
    P1_0 = 0;
    if(P1_4==0) {Delay(20); while(P1_4==0); Delay(20); KeyNumber=1;}
    if(P1_5==0) {Delay(20); while(P1_5==0); Delay(20); KeyNumber=2;}
    if(P1_6==0) {Delay(20); while(P1_6==0); Delay(20); KeyNumber=3;}
    if(P1_7==0) {Delay(20); while(P1_7==0); Delay(20); KeyNumber=10;}
    
    // 扫描第2行
    P1 = 0xFF;
    P1_1 = 0;
    if(P1_4==0) {Delay(20); while(P1_4==0); Delay(20); KeyNumber=4;}
    if(P1_5==0) {Delay(20); while(P1_5==0); Delay(20); KeyNumber=5;}
    if(P1_6==0) {Delay(20); while(P1_6==0); Delay(20); KeyNumber=6;}
    if(P1_7==0) {Delay(20); while(P1_7==0); Delay(20); KeyNumber=11;}
    
    // 扫描第3、4行...
    
    return KeyNumber;
}
```

### 2. LED显示控制

#### 2.1 数码管动态显示
**例题18**：编写8位数码管动态显示程序？

**解答**：
```c
unsigned char code DigTable[] = {0x3F,0x06,0x5B,0x4F,0x66,0x6D,0x7D,0x07,0x7F,0x6F};
unsigned char DisplayData[8] = {0,1,2,3,4,5,6,7};

void DigitalTube_Display() {
    static unsigned char i = 0;
    
    P0 = 0x00;           // 消隐
    P2 = ~(0x01 << i);   // 位选
    P0 = DigTable[DisplayData[i]]; // 段选
    
    i++;
    if(i >= 8) i = 0;
}

// 定时器中断中调用显示函数
void Timer0_ISR() interrupt 1 {
    TH0 = 0xFC;
    TL0 = 0x18;          // 1ms中断
    DigitalTube_Display();
}
```

### 3. 跑马灯程序

#### 3.1 多种跑马灯模式
**例题19**：编写跑马灯程序，要求：A.单个灯循环8轮；B.两个相邻灯循环4轮；C.两个不相邻灯循环2轮；D.全亮全灭各1次？

**解答**：
```c
#include <reg52.h>
#include <intrins.h>

void Delay(unsigned int ms);

void main() {
    unsigned char i, times;
    
    while(1) {
        // A. 单个灯循环8轮
        times = 8;
        while(times--) {
            P1 = 0xFE;       // 第一个灯亮
            for(i=0; i<8; i++) {
                P1 = _crol_(P1, 1);
                Delay(500);
            }
        }
        
        // B. 两个相邻灯循环4轮
        times = 4;
        while(times--) {
            P1 = 0xFC;       // 前两个灯亮
            for(i=0; i<8; i++) {
                P1 = _crol_(P1, 1);
                Delay(500);
            }
        }
        
        // C. 两个不相邻灯循环2轮
        times = 2;
        while(times--) {
            P1 = 0xAA;       // 间隔亮
            for(i=0; i<8; i++) {
                P1 = _crol_(P1, 1);
                Delay(500);
            }
        }
        
        // D. 全亮全灭各1次
        P1 = 0x00;          // 全亮
        Delay(1000);
        P1 = 0xFF;          // 全灭
        Delay(1000);
    }
}

void Delay(unsigned int ms) {
    unsigned int i, j;
    for(i=0; i<ms; i++)
        for(j=0; j<120; j++);
}
```

---

## 📊 题型分布统计

### 按知识点分类
1. **中断系统**：25题（16.7%）
2. **接口技术**：30题（20.0%）
3. **定时器应用**：20题（13.3%）
4. **串行通信**：15题（10.0%）
5. **存储器管理**：18题（12.0%）
6. **汇编语言编程**：22题（14.7%）
7. **综合应用**：20题（13.3%）

### 按题型分类
1. **选择题**：40题（26.7%）
2. **填空题**：35题（23.3%）
3. **计算题**：25题（16.7%）
4. **简答题**：20题（13.3%）
5. **编程题**：30题（20.0%）

### 按难度分类
1. **基础题**：60题（40.0%）
2. **中等题**：70题（46.7%）
3. **提高题**：20题（13.3%）

---

## 🎯 重点题型总结

### 必考计算题
1. **中断向量表地址计算**
2. **物理地址计算**
3. **定时器初值计算**
4. **波特率和时钟频率计算**
5. **8253分频计算**

### 必考编程题
1. **8259A中断控制器初始化**
2. **8255A并行接口编程**
3. **8253定时器编程**
4. **8051定时器应用**
5. **串行通信编程**

### 必考应用题
1. **矩阵键盘扫描**
2. **数码管显示**
3. **LED控制**
4. **PWM波形生成**
5. **中断服务程序设计**

---

## 📝 答题技巧

### 计算题技巧
1. **公式记忆**：物理地址 = 段地址×16 + 偏移地址
2. **进制转换**：熟练掌握十六进制与二进制转换
3. **步骤清晰**：写出计算过程，避免直接写答案

### 编程题技巧
1. **格式规范**：注意汇编语言和C语言的语法格式
2. **注释清楚**：关键步骤要有注释说明
3. **逻辑正确**：先理清程序逻辑再编写代码

### 应用题技巧
1. **理解原理**：深入理解芯片工作原理
2. **接口分析**：正确分析硬件连接关系
3. **时序考虑**：注意时序要求和延时处理

---

*本题目数据库收集了150+道微机原理及单片机应用的经典考试题目，涵盖了课程的所有重要知识点，为期末考试复习提供全面的题目资源。*
