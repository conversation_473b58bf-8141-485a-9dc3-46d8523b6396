

=== 第 1 页 ===

微机原理及单片机应用 
期末总结  
WMY 
按照CJN老师的复习课整理
因为你
不会，所以你才会  
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 2 页 ===

主机CPU+
接口第一章 微型计算机概述 期末总结
2020年7月28日1:01
 2 0 2 0 - 0 8 - 1 0
{,   1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 3 页 ===

40个引脚○
16位数据线，一次传两字节的数据○
20位地址线，直接寻址1MB 存储空间2^16bit = 64KB      
2^20bit = 1MB ○8086是16位微处理器•
准16位处理器，内部操作是16位，对外数据总线只有8 位。 ○
由于当时外围接口芯片都是8 位数据引脚，所以，接线时不需错位连接。 ○关于8088•二、8086CPU 的编程结构
CPU能同时处理的数据位数，也称数据宽度。○
字长越长，计算能力越高，速度越快。○
8086是16位字长。○CPU的字长•
CPU的时钟频率，工作频率。○
主频越高，运算速度越快。○CPU的主频•一、CPU的性能指标
6字节16位第二章 16位微处理器8086 期末总结
2020年7 月28日1:09
 2 0 2 0 - 0 8 - 1 0
{,   2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 4 页 ===

CPU与存储器之间的数据传送
CPU与I/O端口之间的数据传送功能：○
4个段地址寄存器
在段式内存管理中用来存放段地址的寄存器。
CS 代码段
DS 数据段
ES 附加段
SS 堆栈段
指令指针寄存器IP组成：○
取指令之前要先把IP清零地址加法器
产生20位地址，CS左移4 位加上IP，就是20位地址。
例：CS=FE00H,  IP=0200H,  物理地址 = FE200H 。
6Byte 指令队列缓冲器 
用于存放从内存中取出的，将要执行的一或多条指令。      用于存放待取指令的地址‐‐指令所在内存单元的地址。
BIU要从内存取指令送到指令队列○总线接口部件BIU（Bus  Interface  Unit） •
功能：指令的执行○
通用寄存器    AX   BX   CX   DX        存储要计算的数据 
专用寄存器    BP   SP   SI   DI        存储单元的地址 
算术逻辑部件  ALU 
标志寄存器：用于存放ALU工作时产生的状态信息。组成：○
反映了ALU当前的工作状态，是在程序执行过程中，根据指令执行情况，由
CPU内部的硬件 自动置位或清零的；
这些状态信息可作为后续条件转移指令的转移条件。（1）状态标志位：
用于控制CPU的某种操作，是 人为设置的（通过专用指令）（2）控制标志位：执行部件EU（Execution  Unit） •
 2 0 2 0 - 0 8 - 1 0
{,   3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 5 页 ===

标志寄存器FR（Flag  Register ） •
进位标志位CF：最高的一位产生进位或借位为1 ，否则为0 ○
辅助进位位AF：D3向D4进位或借位则为1，否则为0○
零标志位ZF：当前计算结果各位全零则为1 ，否则为0 ○
偶标志位PF：当前计算结果中， 低八位中1 的个数为偶数个则为1 ，否则为0 。常用
于奇偶校验○
符号标志位SF：针对有符号数计算，当前结果的符号位是几该标志位就是几○
溢出标志位OF：针对有符号数计算，如果产生溢出则为1 ，否则为0 ○
中断允许位IF：置1 为允许CPU响应可屏蔽中断请求，置0 相反 ○
单步标志位TF：置1 位CPU每执行一条指令就产生一个单步中断，可查看当前状
况，每步都有断点，置0 则正常运行○
方向标志位DF：针对串操作指令，控制指针加量还是减量移动（先不管）○
例子：
0010 0011  0100 0101
+0011 0010  0001 1001
0101 0101  0101 1110
SF=0；最高位为0
ZF=0；并不是所有位都是0
PF=0；第八位1 有5个
CF=0；最高位没产生进位
AF=0；D3没向D4进位
OF=0；没溢出
时钟周期○
每两个时钟脉冲上升（下降）沿之间的时间间隔由CPU的 主频决定
概念•三、总线周期
 2 0 2 0 - 0 8 - 1 0
{,   4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 6 页 ===

CPU的总线接口部件完成一次访问内存( 或I/O端口) 操作所需要的时间，称
为一个总线周期。总线周期（Bus  Cycle ） ○
一个总线周期至少包括4 个时钟周期:T1,T2,T3,T4 状态 ○
读数据时，T2先进入高阻态，等待数据接收，到T3开始输入数据○
写数据时，T2就开始输出○
在两个总线周期之间没有读写时，执行空闲周期T1，此时输入停止，输出持续○基本总线周期•
系统的复位和启动操作 RESET 信号输入8086 •
在任意时刻接收采样信号，在下一个时钟周期上升沿内部RESET 置高电平， 持续不少于
四个时钟周期才能复位
内部RESET 置高电平后，输出信号的三态门在一个周期后关闭，变成浮空的高阻态
复位后内部寄存器的地址•
四、8086的操作和时序
高阻态
FFFF0H
 2 0 2 0 - 0 8 - 1 0
{,   5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 7 页 ===

复位后启动执行的首条指令的地址是 FFFF0H 。
在FFFF0H 处存放一条无条件转移指令，转移到系统程序的入口处。
主模块：CPU、DMAC等，可以发送使用总线的请求信号
从模块：被动接受寻址信息主模块和从模块○
最小模式下的总线保持（使用HOLD和HLDA 联络信号） ○
①DMAC向8086请求允许使用总线，HOLD 变为高电平，CPU在上升沿对HOLD信号进
行检测，在当前总线周期的T 4之后或空闲的T I周期之后的下一个时钟周期，CPU允
许使用，将HLDA置为高电平。
②HLDA变为高电平后，左侧方框中的引脚进入高阻态，被占用，ALE 脚不浮空。
③当DMCA发出还的信号，HOLD变为低电平之后，CPU在上升沿对HOLD信号进行检
测，在紧接着的下降沿将HLDA拉低，左侧方框的引脚随后恢复正常总线控制权的交接•
 2 0 2 0 - 0 8 - 1 0
{,   6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 8 页 ===

构成•五、8086在最小模式下的典型配置
(DEN)' ：数据总线允许，用来控制收发器的(OE)' ，激活数据总线缓冲器 
DT/R' 、8286的T ：控制方向，T=1  写A‐>B 收发器‐ >数据总线，T=0  读
B‐>A 数据总线‐ >总线收发器
(WR)' 、(RD)' ：指示读、写，在总线周期特定时候变为低电平 
3个锁存器，有22个引脚的信息需要锁存，浪费两个○
所有控制引脚在最小模式下延伸后直接形成控制总线○
引脚是分时复用的○
3个总线是独立的○
1片8284A ，作为时钟发生器 
3片8282地址锁存器
2片8286总线收发器（外设多）
MN/(MX)' ：接+5V（高电平），最小模式工作 
ALE：高电平， 允许锁存信号（在总线周期前一部
分时间，告示地址已准备好）
(BHE)' 信号也需要锁存 
HOLD，HLDA：总线请求/ 允许 
INTR，(INTA)' ：中断请求/ 允许 
M/(IO)' ：区分访问存储器还是I/O 
(OE)' ：控制锁存器、收发器输出端三态门（缓冲
器），锁存器通常直接接地标注○
 2 0 2 0 - 0 8 - 1 0
{,   7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 9 页 ===

在最小模式下读操作（入CPU）•
①M/(I/O)'  根据读取设备（存储器/I/O）输出高或低电平，在整个总线周期保
持
②地址/ 状态线A 19/S6~A16/S3传输高4 位地址，地址/ 数据线AD 15~AD 0输出低16位地
址   只在T1输出地址，地址传送到存储器或I/O 端口。
③ALE在仅T1期间变为高电平（正脉冲），在此期间M/(I/O)'  和地址信号均已有
效，ALE的下降沿使20位地址信息全部被锁存，稳定送到地址总线，用来寻址。④如果访问奇地址，(BHE)'/S
7 仅在T1期间变为低电平，输出高八位数据总线允
许信号，(BHE)' 也会被锁存，(BHE)' 是奇地址存储体的体选信号。（偶地址的是
A0）
⑤如果有总线收发器，DT/R'  调整数据传输方向变为低电平，进入接收数据模式
(DEN)' 不论读写都是在T 1为高电平，T 2由高变低，T 4由低变高（有总线收发
器）。T1
⑥地址/ 状态线A 19/S6~A16/S3在T 2不再输出地址，输出状态信息，直到T 4结束。
⑦AD 15~AD 0在T 2变为高阻态，不再输出地址（20位地址都已锁存）。
⑧(BHE)'/S 7从T 2开始恢复高电平，状态输出S 7（目前S 7无实际意义）。
⑨(RD)'  从T 2变为低电平，指示读操作，T 4变回高电平。
⑩(DEN)'  从T 2到T 4变为低电平，总线收发器获得数据允许信号。T2
⑪AD15~AD 0从T 3开始数据输入，所有状态输出继续保持，持续到T 4结束。T3
从T 3的下降沿开始每周期下降沿采样READY 信号，若为0 则不断插入T w周期，直到
出现1 ，进入T 4。Tw
在T 4和前一个T 的交界下降沿处，CPU的对数据总线进行采样，从而获得数据。T4
 2 0 2 0 - 0 8 - 1 0
{,   8ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 10 页 ===

在最小模式下写操作（出CPU）•
①M/(I/O)'  根据读取设备（存储器/I/O）输出高或低电平，在整个总线周期保
持
②地址/ 状态线A 19/S6~A16/S3传输高4 位地址，地址/ 数据线AD 15~AD 0输出低16位地
址   只在T1输出地址，地址传送到存储器或I/O 端口。
③ALE在仅T1期间变为高电平（正脉冲），在此期间M/(I/O)'  和地址信号均已有
效，ALE的下降沿使20位地址信息全部被锁存，稳定送到地址总线，用来寻址。④如果访问奇地址，(BHE)'/S
7 仅在T1期间变为低电平，输出高八位数据总线允
许信号，(BHE)' 也会被锁存，(BHE)' 是奇地址存储体的体选信号。（偶地址的是
A0）
⑤如果有总线收发器，DT/R'  调整数据传输方向变为高电平，进入输出数据模式
(DEN)' 不论读写都是在T 1为高电平，T 2由高变低，T 4由低变高（有总线收发
器）。T1
⑥地址/ 状态线A 19/S6~A16/S3在T 2不再输出地址，输出状态信息，直到T 4结束。
⑦地址数据线AD 15~AD 0在T 2不再输出地址，输出数据，直到T 4结束
⑧(BHE)'/S 7从T 2开始恢复高电平，状态输出S 7（目前S 7无实际意义）。
⑨(WR)'  从T 2变为低电平，指示写操作，T 4变回高电平。
⑩(DEN)'  从T 2到T 4变为低电平，总线收发器获得数据允许信号。T2
读写操作只有⑤⑦⑨ ⑪不同，其他完全一致
 2 0 2 0 - 0 8 - 1 0
{,   9ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 11 页 ===

CPU执行程序过程中，发生了某种随机事件（CPU内部或外部）使CPU暂时中
断正在运行的程序，转去执行【中断服务子程序】以处理该事件，事件处理
完后又被中断的程序继续执行的过程称为中断。概念○
引起CPU中断的事件叫做中断源。中断源○
分为硬件中断和软件中断，学习硬件中断为主。
硬件中断是通过外部硬件产生的又称为外部中断。
非屏蔽中断：通过CPU的NMI引脚进入，不受中断允许标志IF屏蔽（唯
一）。□
可屏蔽中断：通过CPU的INTR引脚进入，只有IF为1 时，可屏蔽中断才
能进入，如IF为0 则中断被禁止，通过中断控制器8259A 配合工作可屏
蔽中断可以有很多。□硬件中断分为两类分类○
中断后的第一条指令，或被紧接着执行中断的指令的语句的下一条语句。断点○
触发中断后自动根据中断向量表填写CS、IP执行子程序，不能手动调用。中断服务子程序○中断的基本原理、概念•
8086可以处理256个中断源。
中断类型号的范围是00H‐ FFH。 
在第一个周期告诉外设准备中断类型号。□
在第二个周期把中断类型号放到数据总线上，供CPU读入。□CPU响应INTR中断时，会产生两个中断响应的总线周期中断类型号○
中断向量是每个中断源的 中断服务子程序的入口地址。 
中断向量由 偏移量（ IP，低字）和段基址（ CS，高字）组成 中断向量○
中断向量表是一段连续的存储单元。
中断向量表存放了 256个中断向量，即256个中断服务子程序的入口地址。 
中断向量表的位置是00000H~003FFH ，共1KB。 
每个中断向量4B，低字是偏移量IP，高字是段基址CS。中断向量表○中断类型号、中断向量、中断向量表•六、中断操作和中断系统
 2 0 2 0 - 0 8 - 1 0
{,   1 0ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 12 页 ===

填表在系统初始化的时候自动完成，查表在中断响应过程中自动执行
中断类型号× 4即为中断向量的起始地址（4 个字节中最小的所在地址） 
即0000: 中断号× 4   ‐ >  中断号× 4前面补一个零
不论怎么寻址都是   CS:IP 格式填表和查表（必考）○
例子，假设中断类型码为3H，对应中断服务子程序的入口地址是1E00:0A00H ，填
写中断向量表
中断类型码× 4之后的地址对应的是 IPL，即入口地址最后两位
低IP
高CS
0000FH
 2 0 2 0 - 0 8 - 1 0
{,   1 1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 13 页 ===

27H× 4 即为0010  0111左移2 位，即1001  1100，即9CH
所以中断向量表应填写在从0000:009CH 开始的连续4 个字节中
0000:009FH 60H
0000:009EH 20H
0000:009DH 04H
0000:009CH 1AH例子，假设中断类型码是27H，对应中断服务子程序的入口地址是6020:041AH ，
填写中断向量表
例子，假设在中断向量表中，连续的4 个字节存储单元存储了某个
中断源对应的中断服务子程序的入口地址1020H:  7080H
(0000:005CH)=80H
(0000:005DH)=70H
(0000:005EH)=20H
5CH  = 0101  1100B ，5CH÷ 4 = 0001  0111B = 17H
故中断类型号是17H (0000:005FH)=10H
从NMI引脚进入的中断为非屏蔽中断，不受中断允许标志IF的影响。非屏蔽
中断的类型号为2 ，所以非屏蔽中断处理子程序的入口地址存放在00008H~
0000BH 中
NMI引脚出现中断请求，CPU不管正在干什么都无条件处理该中断，该中断优先级非常高。
实际应用中，一般处理重大故障用，如掉电。NMI○
一般的外部设备发出的中断都是从CPU的INTR端引入的可屏蔽中断。
详见下面↓INTR○硬件中断由两条引脚送入CPU•
当CPU接收到一个可屏蔽中断请求信号(INTR=1) 时，如果标志寄存器的IF=1 ，则
CPU会在
执行完当前指令之后响应这一中断请求。○
CPU往(INTA)' 引脚发送两个负脉冲到外设作为应答信号。第二个脉冲后，外设将
中断类型号发给CPU，CPU开始响应中断。○
从数据总线上读取中断类型号，存在内部暂存器。
将标志寄存器的值入栈保护（标志寄存器的保护由硬件自动实现）。
IF清零：在中断响应过程中暂时屏蔽其他外部中断。□
TF清零：避免CPU 以单步的方式执行中断处理子程序。 □把标志寄存器的中断允许标志IF和跟踪标志TF置零具体响应过程○可屏蔽中断的响应过程（毕业生考试考了）•七、硬件中断
 2 0 2 0 - 0 8 - 1 0
{,   1 2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 14 页 ===

将断点（CS，IP）入栈保护（断点的保护由硬件自动实现）。
根据中断类型号，查中断向量表，低字填IP，高字填CS，然后转入相应的中
断处理子程序。
与可屏蔽中断基本相同，只有中断类型码02H ，不需要从外设读取中断类型码。 ○
低字填IP，高字填CS，然后转入相应的中断处理子程序。○非可屏蔽中断的响应过程•
方法1○
CLI
MOV  AX, 0
MOV  DS,  AX  ; 段基址置零
MOV  SI,  08H*4  ;计算中断处理子程序对应中断向量的起始地址
MOV  AX, OFFSET SER08  ;IP
MOV  [SI], AX
MOV  AX, SEG SER08 ;CS
MOV  [SI+2],  AX
;(允许8255A 提中断、8259A的操作命令字OCW设定 )
STI
方法2○
CLI
XOR  AX, AX
MOV  DS,  AX  ; 段基址置零
MOV  AX, OFFSET SER08  ;IP
MOV WORD
 PTR[0020H],  AX ;提前算好中断向量的起始地址
MOV  AX, SEG SER08 ;CS
MOV  WORD PTR[0022H],  AX
;(允许8255A 提中断、8259A的操作命令字OCW设定 )
STI初始化中断向量表，假设中断处理子程序的标号是SER08 ，中断类型号是08H •
FAR过程
常驻内存，固定装配特点○
保存有关寄存器的内容
若允许8259A 中断嵌套则开中断 
中断处理（其他操作）
关中断，并发送中断结束命令给8259A
恢复寄存器内容
返回（IRET）8086中断处理子程序架构○中断服务子程序（8086）•八、中断向量表的初始化和中断处理子程序
 2 0 2 0 - 0 8 - 1 0
{,   1 3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 15 页 ===

8086系统中，1M字节分为2 个512K的存储体 ○
偶体‐‐存储体中的每一个存储单元的地址都是偶数○
奇体‐‐存储体中的每一个存储单元的地址都是奇数○
(BHE)' 作为奇地址存储体的提选信号，具体的地址由A1‐ A19决定 ○8086存储器组织•九、8086的存储器编址和I/O编址例子○
……DATA  SEGMENT
ENDS
STACK SEGMENT
DW   128  DUP(0)
ENDS
CODE1 SEGMENT
CLIMOV  AX, 0
MOV  DS,  AX  ; 段基址置零
MOV  SI,  08H*4  ;计算中断处理子程序对应中断向量的起始地址
MOV  AX, OFFSET SER08  ;IP
MOV  [SI], AX
MOV  AX, SEG SER08 ;CS
MOV  [SI+2],  AX
;(允许8255A 提中断、8259A的操作命令字OCW设定 )
STIMAIN: （初始化以外的操作省略）
CODE1 ENDS
CODE2 SEGMENT      ;代码段2 
INTCODE   PROC    FAR    ; 中断服务子程序 远过程
STI
…(具体操作)
CLI
;发送中断结束命令（8259 A，见第七章）
IRET  ;中断返回SER08:   
INTCODE  ENDP
CODE2  ENDS
注：初始化时用的是子程序的标号而不是程序名。
       进入子程序后要开中断，中断返回前要关中断，发送中断结束命令。
IP、CS、Flag 依次出栈恢复 ○中断返回（IRET）•
 2 0 2 0 - 0 8 - 1 0
{,   1 4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 16 页 ===

奇地址存储体是高八位，偶地址存储体是低八位
8086有20根地址线，存储空间1MB, 地址宽度应为20位Bits,  00000H~FFFFFH ○
64KB=65355Byte=216Byte=64*1024*Byte 8086  CPU的大多寄存器都是16位，只能直接寻址64KB 。 ○
物理地址的计算方法： 段基址左移 4位+偏移量 ○
在不同的段，形成二十位地址的不同寄存器组合：○8086存储器的编址•
8086系统和外部设备之间是通过I/O芯片来联系的。○
每个I/O芯片有一或多个端口，每个端口对应芯片中的一个寄存器。○
微机系统要为每个端口都分配地址，称为端口号。○
端口号的地址编码采用16位编码0000 ‐FFFFH 。 ○
可寻址的I/O端口数为64K(65535) 个，I/O端口的宽度普遍是8 位（数据）。 ○8086的I/O编址•
 2 0 2 0 - 0 8 - 1 0
{,   1 5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 17 页 ===

I/O芯片的数据引脚通常直接挂在数据总线的最低八位上。○
8086对接口部件的地址分配是 连续的偶地址，配合16位数据总线。 
要根据端口数量决定用多少根地址线对端口进行片选，如4 端口用 2位地址线 接口部件的地址引脚和系统的地址总线 错位连接 ○
A19 A18. …A4 A3 A2 A1 A0
0 0 0 A
0 0 1
0 1 0 B
0 1 1
1 0 0 C
1 0 1
1 1 0控
1 1 1
在16位微机系统的内存组织中
奇地址对应高八位数据总线偶地址对应低八位数据总线
A
0为0激活偶体，且接口部件的数据线默认
连接数据总线的最低八位，因此需要给接
口部件分配连续的偶地址8255A
地址线连接数据线连接
A：80H
B：82H
C：84H
控：86H
A0
 功 能 数 据
0    0    同时访问偶存储体和奇存储体 D15 ~ D0
0 1 偶存储体 D7 ~ D0
1 0 奇存储体 D15 ~ D8
1 1 两体均未选中 无
 2 0 2 0 - 0 8 - 1 0
{,   1 6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 18 页 ===

补充一点关于MOV等指令：两个操作数的长度一定要一致•
MOV  AL, 80H 
MOV  AX, 1090H 例子○
不需要总线周期，执行速度快○立即数寻址•
INC  CX
ROL  AH,1  ;左移1 位 例子○
不需要总线周期，执行速度快○寄存器寻址•
I/O直接寻址（端口号范围0~FFH ） ○
地址直接在指令中提供
I/O间接寻址（端口号范围0~FFFFH ） ○
地址需要在DX中提前设置
I/O端口寻址•
用BP、SP间接寻址时，默认的段是SS（堆栈段），其他寄存器寻址默认段为DS○
MOV  AX,  [BX]       ;把DS段BX所指地址开始2 字节的内容送到AX 
MOV  EAX,  [BP]      ;把SS段BP所指地址开始4 字节的内容送到EAX 例子○
对非DS且非SS段（非默认段）寻址时，需要标注指定段○
MOV  CX,  ES:[BX]    ;把ES段（指定）BX所指地址开始的2 字节内容送到CX 例子○寄存器间接寻址【重点】•
MOV  AX,  [SI+100H]  ;把DS段SI+100H 所指地址开始的2 字节内容送到AX 例子○寄存器相对寻址（数组、表格）/ 带位移量的寄存器间接寻址 •
例子○
注：基址加变址寻址也遵循寄存器间接寻址中默认段和非默认段的使用方式○基址加变址寻址•一、8086的寻址方式第三章 8086汇编指令系统 期末总结
2020年8 月1日2:52
      
 2 0 2 0 - 0 8 - 1 0
{,   1 7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 19 页 ===

格式：MOV  <目标操作数>,  <源操作数> ○
源操作数和目标操作数的位数需要一样。○
源操作数和目标操作数中至少有一个是寄存器。○
不能在两个内存单元之间直接传数据，要借助通用寄存器。○
不能从一个段寄存器向两一个段寄存器传操作数，借助AX寄存器。○
CS、IP、EIP 不能作为目标操作数。 ○
间接寻址时，用BP、SP，默认的段是SS（堆栈段），其他寄存器寻址默认段为
DS，非默认段寻址时需要把制定的段标注（见寻址方式）。○
所有通用传送指令都不改变标志。○通用传送指令MOV•
格式：PUSH  SRC  / POP  SRC ，长度通常是字或双字。不可以PUSH  AL。 ○
寻址：SS:SP○
先入后出。○
这里的堆栈和数据结构中学的地址是相反的。○
堆栈的最高的地址是栈底，入栈指令使栈顶地址减字节，出栈使栈顶地址加字节。○
堆栈段指针SP始终指向栈顶地址，SP的初值为栈底地址加一○
以AX=1020H 为例，入栈和出栈举例 ○堆栈操作指令PUSH、POP•
入栈，SP‐ 1，AH入栈1 字节，SP再‐ 1，AL入栈1 字节 
出栈，出栈1 字节到AL，SP+1 ，再出栈1 字节，SP+1 二、传送指令
【SP变化要求掌握】
      
 2 0 2 0 - 0 8 - 1 0
{,   1 8ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 20 页 ===

入栈时，栈内已有内容不便修改，需要先移动SP。
出站时，SP不能没有理由改变所指的实单元，因此先出栈（强行解释）。SP始终指向实单元○
格式：XCHG  <操作数1>,  <操作数2>    长度不限，但要求一致。 ○
操作数不能是内存单元○
XCHG  AL, BL 
XCHG  [2530H],  CX      ;交换DS:2530H 地址对应的值和CX 例子○交换指令XCHG•
直接的I/O指令【后几章主要用的方式】，范围是0~FFH○
IN AL,  50H     ;将50H端口的字节读入AL 
OUT  80H  , AX   ;将AX中的内容输出到80H 、81H两个端口（长度一致）。 例子○
间接的I/O指令，用DX寄存器设置端口号，范围是0~FFFFH 。 ○
MOV  DX, <0~FFFFH>    
其余同上例子○输入/ 输出指令IN/OUT •
格式：XLAT○
使用条件：首先要有一个表，将表的 偏移量赋给 BX
，将索引值赋给AL。 ○
结果：XLAT执行之后，AL中的内容就变为了[BX+AL] 。 ○
表的数据类型必须是字节，因为提取的内容存到AL，有长度限制。
索引值为0~FFH使用限制：○换码指令/ 查表转换指令XLAT •
最常用格式：LEA  SI,  <标号> ○
等效指令：MOV  SI,  OFFSET <标号> ○取有效地址指令LEA•
这两个指令都会影响状态标志位。
最常用的是ADD指令，ADC配合ADD在长数据相加中使用。
格式：ADD  CX,  1000H   ;把CX中的值和1000H 相加，存进CX。 
计算两个8 字节加法，先用ADD把低4 字节相加，影响CF，然后ADC 指令加高
八位，并会加上CF位，完成相加。ADD无进位/ADC有进位○
格式：INC  CX INC增量指令○加法指令•三、算数指令
      
 2 0 2 0 - 0 8 - 1 0
{,   1 9ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 21 页 ===

INC指令也会改变状态标志位但不会影响CF位。
和ADD/ADC 相反 SUB无借位/SBB有借位○
与INC相反，也不影响CF位减量指令DEC○
格式：NEG  <操作数>  ;对操作数求补码并改变操作数。 
求补指令影响状态标志位。
求补指令等价于0 减去该操作数。 
除非操作数是0 ，否则CF在执行完求补指令后一定是1 。 
80H、8000H 、80000000H 求补结果不会变化，但是OF会置1 。 求补指令NEG○减法指令•
格式：CMP  <操作数1>,  <操作数2>  ;操作数1 减操作2 ○
该指令操作为操作数1 减操作2 ，不返回结果，只改变状态标志位。 ○
操作数相等：ZF=1
操作数1 大于操作数2 ：无借位，CF=0 
操作数1 小于操作数2 ：有借位，CF=1 无符号数比较结果：○
操作数1 大于操作数2 ：SF=0 □
操作数1 小于操作数2 ：SF=1 □不出现溢出情况
操作数1 大于操作数2 ：SF=1 □
操作数1 小于操作数2 ：SF=0 □出现溢出OF=1有符号数比较结果：○比较指令CMP•
格式：MUL  <乘数>   ;乘数与AX或AL中的内容相乘，存入DX，AX 或AX中。 ○
8位数据和8 位数据相乘会得到16位的值，DX作为AX高16 拓展位 （16位乘
16位）。 ○
最常用的是8 位数据乘，所得结果存在AX中。 ○
MUL是无符号数乘法IMUL是有符号数乘法。○乘法指令MUL/IMUL•
与乘法指令类似，对于长数据类型需要用到DX作为高 16拓展位 ○
两个例子○
除法指令DIV/IDIV•
异号相加不会溢出，同号相加可能溢出○
OF = C 7⊕C6   C7是最高位进位，C 6是次高位进位， ○溢出之双高位判别法•
BCD码调整指令•
      
 2 0 2 0 - 0 8 - 1 0
{,   2 0ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 22 页 ===

如果数值大于9 则加6 处理 
如果计算结果产生十进制进位，则加6 处理 BCD调整○
该指令用于二进制加法运算之后，且必须加在AL上
执行后将AL（4 位）中的值变成非压缩BCD码（8 位），存放在AH，AL中。 
例子
MOV  AL,39H
ADD  AL, 17H
AAA
AX = 0050H =>AX  = 0056H加法非压缩BCD码调整指令AAA○
该指令用于二进制加法之后，且必须加在AL上
执行后将AL（4 位）中的值变成压缩BCD码（4 位），存放在AL中。 
例子
MOV  AL,39H
ADD  AL, 17H
DAAAL = 50H =>AL = 56H加法压缩BCD码调整指令DAA○
类似AAA‐ >AX 减法非压缩BCD码调整指令AAS○
类似DAA‐ >AL 减法压缩BCD码调整指令DAS○
类似AAA‐ >AX 乘法非压缩BCD码调整指令AAM○
类似AAA‐ >AX 
特别注意，
其他运算是先算再调，除法是先调再算 除法非压缩BCD码调整指令AAD○
BCD码调整指令会影响状态标志位○
格式：AND  <寄存器>,  <操作数> 
将指定的寄存器中的数与操作数进行逻辑运算，结果存在指定的寄存器中。
除NOT外都会影响标志位，OF、CF置0。AND、OR、NOT 、XOR   与、或、非、异或 ○
执行AND的操作，但不返回结果，仅影响标志位，并将OF、CF置0。
TEST主要看ZF位。TEST测试○
逻辑运算指令•
      
 2 0 2 0 - 0 8 - 1 0
{,   2 1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 23 页 ===

逻辑运算指令需要注意以下几点：○
移位指令，了解•
串操作指令，看作业，了解•
      
 2 0 2 0 - 0 8 - 1 0
{,   2 2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 24 页 ===

格式：CALL  <子程序第一句的标号> ○调用子程序CALL指令•
子程序结束出栈之后执行该指令。○返回指令RET•
格式：< 转移指令>  <跳转标号或一句指令> ○
如满足条件则执行转移指令后的语句，不满足则执行下一行。○
有符号数比较○
JL/JNGE 小于、不大于/ 不等于则转移 <
JNL/JGE 不小于、大于/ 等于则转移 ≥
JLE/JNG 小于/等于、不大于则转移 ≤
JNLE/ JG 不小于/不等于、大于则转移 >
无符号数比较○
JB/JNAE/JC 低于/不高于或不等于/ 进位为1则转移 <
JNB/JAE/JNC 不低于/高于或等于/ 进位为零则转移 ≥
JBE/JNA 低于/等于、不高于则转移 ≤
JNBE/ JA 不低于/不等于、高于则转移 >
标志位判断○
JZ/JE 结果为零/ 相等则转移 ZF=1
JNZ/JNE 结果不为零/ 不相等则转移 ZF=0
JS 结果为负则转移 SF=1
JNS 结果为正则转移 SF=0
JO 溢出则转移 OF=1
JNO 不溢出则转移 OF=0
JP/JPE 奇偶位为1 则转移 PF=1
JNP/JNPE 奇偶位为0 则转移 PF=0
无条件转移指令 JMP 想跳哪就跳哪 ○转移指令：•
执行LOOP指令时，先将CX‐ 1，然后判断CX是否为0 ，不为0 则跳转到标号处 ○
MOV  CX,  64H   ;设置循环次数
KKK:      ……
          ……
……          LOOP KKK格式：○计数循环指令LOOP•四、程序控制：调用、转移、循环控制、中断指令
      
 2 0 2 0 - 0 8 - 1 0
{,   2 3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 25 页 ===

          MOV  CX,  64H   ;设置循环次数
KKK:      ……
          ……
CMP  CX, 0
JNZ  KKK
……          DEC CX另一种实现方式○
MOV  CX,  64H   ;设置循环次数
KKK:      LOOP KKK延迟程序○
从堆栈弹出IP和CS各2个字节（恢复断点）。
然后弹出标志寄存器的值。中断结束过程（毕业生考试考了）○中断指令INT和中断返回指令IRET•
格式•
<程序名> PROC  <NEAR/FAR>
PUSH  xxx
…;具体操作
POP;  xxx
RET <再起个标号>  ;用于取CS、IP
<程序名> ENDP
近类型NEAR：CALL和子程序处于同一代码段。○
远类型FAR：CALL和子程序不在同一代码段（中断的用的多）。○远近类型•五、子程序
      
 2 0 2 0 - 0 8 - 1 0
{,   2 4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 26 页 ===

3‐1 在数据段中定义一个数组，包含10个元素，每个元素的长度为1 个字，编程：求所
有元素之和，并将和存储在SUM单元。•
data segment
     ; add your data here! 
     ARY      DW 1, 2, 3, 4, 5, 6, 7, 8, 9, 10
     COUNT   DW ($‐ARY)/2         ;数组元素个数
     SUM      DW ?                 ;数组和，存放在SUM
     pkey db "press any key...$"
ends
stack segment
     dw    128  dup(0)
endscode segment
start:                   
; set segment  registers:
     mov  ax, data
     mov  ds, ax
     mov  es, ax
     ; add your code h
ere
;==============================================================  
     LEA  SI,  ARY           ;取数组起始地址
     MOV  CX,  COUNT         ;取元素个数CX=10
     XOR  AX,  AX            ;清0累加器
LOOPER:                    ;搞一个循环
     ADC  AX,  [SI]          ;累加和
     ADD  SI,  2H            ;修改地址指针, 指向下一个数字
     LOOP  LOOPER           ;判断CX是否为零，
                          ;不为零则跳转到LOOPER ，为零则执行下一句
     MOV  SUM,  AX           ;存和
;==============================================================    
     lea  dx, pkey
     mov  ah, 9
     int  21h        ; out
put string at ds:dx
    
     ; wait for any key....     
     mov  ah, 1
     int  21h
     mov  ax, 4c00h ; exit to operating  system.
     int  21h    
ends
end  start ; set entry point and stop the assembler.
;==============================================================  
运行结果：SUM  = 0037H六、作业题整理（我自己写的，整理时没参考老师给的答案，当时都做对了。）
$‐<变量名>/数据长度是取长
度的一种常用方式
把未知的变量设为? 或者0
      
 2 0 2 0 - 0 8 - 1 0
{,   2 5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 27 页 ===

3‐2下表所示，为摩尔斯电码数字编码规则。点用0 表示，长划线用1 表示。 •
1） 先从屏幕显示提示信息，如“ 请输入一个数字0~9 ：”
2） 等待用户从键盘输入一个数字
3） 先将数字转换成非压缩BCD码，再通过查表指令将其转换成对应的摩尔斯编码，
存入某个变量中。要求用 XLAT  指令实现。
data segment
     ; add your data here!  
     PRINT1  DB  'Please  input a number(0~9):','$'
     PRINT2  DB  'MORSE_CODE:  ','$'
     PRINT3  DB   'INDEX:  ','$'                     ;定义提示字符串
     MORSE   DB  '11111'  ;0
             DB  '01111'  ;1
             DB  '00111'  ;2
             DB  '00011'  ;3
             DB  '00001'  ;4
             DB  '00000'  ;5
             DB  '10000'  ;6
             DB  '11000'  ;7
             DB  '11100'  ;8
             DB   '11110'  ;9                         ;定义10个摩斯电码
     INDEX   DB  0
     COUNT   DB  5
     pkey db "press any key...$"
ends
stack segment
     dw    128  dup(0)
endscode segment
start:
; set segment  registers:
     mov  ax, data
     mov  ds, ax
     mov  es, ax
     ; add your code here          
;==============================================================         
     MOV  AH, 9
     LEA  DX, PRINT1
     INT  21H              ;输出"Please  input a number(0~9):"
         MOV  AH, 1
    INT  21H       
       ;从键盘输入一个数字
     AAA                  ;将数字转为非压缩BCD码存在 AH,AL 中
    
     MOV  DX,  AX           ;将索引值在DX中拷贝一份用来最后存储
     MOV  BL, COUNT
     MUL  BL              
     MOV  INDEX, AL       
     ;每个字符串长度为5 ，因此将索引值乘5 ，用于 XLAT指令，使查表的步长为5
         LEA  DI,  MORSE        ;定位查表后的位置，用于最后的输出
     XOR  AH, AH
     ADD  DI, AX
         MOV  BX,  OFFSET MORSE; 开始查表
     LEA  SI, INDEX
     MOV  AL, [SI]
     XLAT     
            ;AL‐>[BX + AL]
     MOV  [SI], DL         ;存索引值
    
      
 2 0 2 0 - 0 8 - 1 0
{,   2 6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 28 页 ===

     MOV  DL, 0DH
     MOV  AH, 02H
     INT  21H              ;回车
     MOV  DL, 0AH
     MOV  AH, 02H
     INT  21H              ;换行
     MOV  AH, 9
     LEA  DX, PRINT2
     INT  21H              ;输出"MORSE_CODE:"
    
     MOV  CL,  COUNT        ;计数循环输出
LOOPER:                   ;将摩斯电码的数字一位一位传入 AL输出
     MOV  AL, [DI]
     MOV  AH, 2           
     MOV  DL,  AL           ;输出目标摩斯电码的一位
     INT  21H
     INC  DI 
     DEC  CL   
     JNZ  LOOPE R
;===
===========================================================      
     MOV  DL, 0DH
     MOV  AH, 02H
     INT  21H              ;回车
     MOV  DL, 0AH
     MOV  AH, 02H
     INT  21H              ;换行
    
     MOV  AH, 9
     LEA  DX,  PRINT3       ;输出"INDEX:  "
     INT  21H                           
         MOV  AL, INDEX
     ADD  AL,  30H          ;将数字转换为ASCII 码
     MOV  AH, 2           
     MOV  DL,  AL           ;输出
 INDEX
     INT  21H
         MOV  DL, 0DH
     MOV  AH, 02H
     INT  21H              ;回车
     MOV  DL, 0AH
     MOV  AH, 02H
     INT  21H              ;换行
                     lea  dx, pkey
     mov  ah, 9
     int  21h         ; output string at ds:dx
         ; wait for any key....     
     mov  ah, 1
     int  21h
         mov  ax, 4c00h ; exit to operat
 ing system.
     int  21h    
ends
end  start ; set entry point and stop the assembler.
等号线之内的是重点。输出方式软中断不是重点。
      
 2 0 2 0 - 0 8 - 1 0
{,   2 7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 29 页 ===

3‐3 自NUM1开始，定义了一个数字字符串，长度不超过20个字符，包含了0~9 的ASCII
码，将字符串中的每一个数字，转换成非压缩BCD码，并放到NUM2 开始的存储单元中。
要求用串指令实现。•
字符串长度为8 字节。
data segment
     ; add your data here!
    
     NUM1     DB  '20200404'
     LEN      DB  $‐NUM1
     NUM2     DB  LEN DUP(0)
     PRINT   DB  'NUM2:','$'
     pkey db "press any key...$"
ends
stack segment
     dw    128  dup(0)
endscode segment
start:
; set segment  registers:
     mov  ax, data
     mov  ds, ax
     mov  es, ax
     ; add your code here
;==============================================================         
     LEA  SI, NUM1
     LEA  DI, NUM2
     MOV  CL,  LEN       ;按NUM1的长度计数
    CLD               ;DF  = 0，SI，DI指针加量变化（串操作专用）
         MOV  AH, 9
     LEA  DX, PRINT
     INT  21H           ;输出'NUM2:'  
    
     MOV  AH,  02H       ;用来在下面的循环体中输出
    
LOOPER:
     LODSB             ;取串（SI指针）
     OR AL,  30H        ;当前数字变成ASCII 码，并且可以避免已经是ASCII 码的数字再加 30
     STOSB             ;存串（DI指针）
     MOV  DL,  [DI‐ 01H]  ;将存进去的一个字符输出
    ;由于STOSB 指令之后 DI
会自动加一，因此源操作数用[DI ‐01H]
     INT  21H
     DEC  CL           
     JNZ  LOOPER
;==============================================================         
     MOV  DL, 0DH
     MOV  AH, 02H
     INT  21H              ;回车
     MOV  DL, 0AH
     MOV  AH, 02H
     INT  21H              ;换行
            
     lea  dx, pkey
     mov  ah, 9
     int  21h         ; output string at ds:dx
    
     ; wait for any key....     
     mov  ah, 1
     int  21h
    
     mov  ax, 4c00h ; exit to operating  system.
     int 21h  
  
ends
end  start ; set entry point and stop the assembler.指定数量的数据类型初始化用
<长度> DUP(< 初始值>)定义
      
 2 0 2 0 - 0 8 - 1 0
{,   2 8ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 30 页 ===

3‐4‐1 定义一个数组，元素为带符号数，求数组元素最大值，存入变量MAX。 •
data segment
     ; add your data here!
     BLOCK  DB ‐ 2, 5, ‐113, 19, 123,
     COUNT  DW   $‐BLOCK   
     MAX  DB   0
     PRINT DB "Max Number of BLOCK:",'$'
    
     pkey db "press any key...$"
ends
stack segment
     dw    128  dup(0)
endscode segment
start:
; set segment  registers:
     mov  ax, data
     mov  ds, ax
     mov  es, ax
     ; add your code h
ere
;==============================================================         
     LEA  SI, BLOCK   ;取BLOCK 地址
     MOV  CX, COUNT
LOOPER:     MOV  AL, [SI]
     CMP  AL, MAX
     JNLE   _MAX_      ;(SF异或CF)并ZF=0
     INC  SI
     DEC  CX 
     JNZ  LOOPER
_MAX_:
     MOV  MAX,  AL      ;将当前最大值赋给MAX
     INC  SI
     DEC  CX 
     JNZ  LOOPER
;==============================================================     
;输出部分省略不考                
     lea  dx, pkey
     mov  ah, 9
     int  21h         ; outpu 
t string at ds:dx
    
     ; wait for any key....     
     mov  ah, 1
     int  21h
         mov  ax, 4c00h ; exit to operating  system.
     int  21h    
ends
end  start ; set entry point and stop the assembler.
      
 2 0 2 0 - 0 8 - 1 0
{,   2 9ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 31 页 ===

3‐4‐2 定义一个数组，元素为带符号数。编写一个有主程序和子程序结构的程
序模块。子程序1的功能是将数组中的元素求绝对值。子程序2的功能是求数组
元素的最大值。主程序调用子程序，求数组元素绝对值的最大值，存入某个变量。•
data segment
     ; add your data here!
     BLOCK  DB ‐ 2, 5, ‐ 113,  19,  123, ‐ 127  ; 加了一个绝对值更大的负数
     COUNT  DW   $‐BLOCK    
     MAX  DB   0
     PRINT1  DB "Max ABS(Number)  of BLOCK: ",'$'
     PRINT2  DB "Maximum  positive  integer:  123",'$'
     PRINT3  DB "Maximum  negative  integer: ‐127",'$'
     pkey db "press any key...$"
ends
stack segment
     dw   128  dup(0)
endscode segmen
t
start:
; set segment  registers:
     mov  ax, data
     mov  ds, ax
;==============================================================  
     CALL _ABS_
     CALL _MAX_
     JMP  END_
;==============================================================  
     ; add your code here
     MOV  CX, COUNT
_ABS_ PROC  NEAR    ;取绝对值子程序
     PUSH  AX        ;保护
     PUSH SI
     PUSH CX
     LEA  SI,  BLOCK ;取地址
     MOV  CX, COUNT
CHECK:
     MOV  AL,  [SI]  ; 逐个检查取绝对值
     OR AL,  AL      ;置标志
  
  JNS  NEXT       ;SF=0 转next；说明是正数
     NEG  AL         ;否则求补，即取绝对值
NEXT:     MOV  [SI], AL  ; 写入已修改或未修改的值
     INC  SI
     DEC  CX
     JNZ  CHECK      ;LOOP CHECK
     POP  CX
     POP  SI
     POP  AX
     RET
_ABS_ ENDP
;==============================================================  
_MAX_ PROC NEAR
     PUSH AX
     PUSH CX
     PUSH SI 
     LEA  SI, BLOCK 
     MOV  CX, COUNT
LOOPER:
     MOV AL, [
SI]
     CMP  AL, MAX
     JNLE  REMAX     ;大于等于当前MAX
     INC  SI
     DEC  CX
     JNZ  LOOPER
REMAX:
     MOV  MAX,  AL   ; 将当前最大值赋给MAX
     INC  SI
     DEC  CX
     JNZ  LOOPER
     POP  SI
     POP  CXOR AL,  AL 指令只会改变标志寄存器的
值，得到的结果还是AL，此时可以通过
SF标志看最高位
      
 2 0 2 0 - 0 8 - 1 0
{,   3 0ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 32 页 ===

     POP  AX
     RET
_MAX_ ENDP 
;==============================================================  
END_:    
;输出部分已省略
       
     lea  dx, pkey
     mov  ah, 9
     int  21h         ; output string at ds:dx
         ; wait for any key....     
     mov  ah, 1
     int  21h
         mov  ax, 4c00h ; exit to operating  system.
     int  21h    
ends
end  start ; set entry point and stop the  assembler.
      
 2 0 2 0 - 0 8 - 1 0
{,   3 1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 33 页 ===

Bit：二进制位○
Byte：字节，1Byte=8Bits○
Word：字，1Word=2Bytes=16Bits○
1K = 210= 1024 ○
1M = 220= 1K×1K ○
1G = 230= 1K×1M ○
1T = 240= 1M×1M ○术语和换算关系•
定义：容量值一个存储器芯片能存储的二进制信息。○
存储器芯片容量 = 存储单元数×每单元的数据位数。 ○
6264 8KB  = 8K×8Bit 
6116 2KB  = 2K×8Bit 例子○容量•一、存储器的主要技术指标
插入T w高速CPU和低速存储器之间的速度匹配问题○
小系统直连，大系统加驱动器CPU总线负载能力问题：○
片选信号和行地址、列地址的产生机制○
地址总线的低位地址线直接和各存储芯片的地址线连接，用于片内寻址。
所用低位线数目N 和存储器的容量2N有关。 
地址总线余下的高位地址线经过译码后用作各存储芯片的片选，通常一些控
制信号也参与译码。(M'/IO 经常用)对芯片内部的寻址方法○存储器和CPU的连接考虑•
核心：使用高位地址线产生“片选”，分为地址译码法和线性选择法。○
直接用一根地址线对应一个存储器作为片选信号。
缺点：地址不连续、地址重叠。线性选择法○
寻址未用的 全部高位都参与译码。 
一般使用译码器或可编程器件等实现。全译码法○片选信号的产生方法【要复习掌握下面的几个例子】•二、存储器的连接
]第四章 I/O端口地址译码和存储器拓展 期末总结
2020年8月4日1:25
      
 2 0 2 0 - 0 8 - 1 0
{,   3 2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 34 页 ===

优点：存储器空间地址连续，每个存储单元地址唯一。 
寻址未用的 部分高位参与译码。 
缺点：地址重叠，使用恰当的组织方式可以避免。部分译码法○
线性选择+ 部分译码。 
缺点：地址不连续、地址重叠。混合译码法○
译码器接线（考试应该会给… 吧） ○
全译码例子○
用4片6264(8KB) 构成32K× 8(Bits) 存储区，使用全译码法，求整个32K× 8存储区
的地址范围
首先分析单片6264，单片6264是8KB，这个题分析的是地址，最小单位是Byte 。
数据总线是8 位对应8Bits ，与地址无关。
8K = 23×210 ，所以需要13位地址线进行片内寻址，对应图中每片的A 0~A12。
所以A 13~A19全部用于译码，各片的(CS)' 作为片选端。
由于译码器是2 ‐4译码器，只有A 13、A 14用来输入片选信息，剩下的引脚全部为0.
↑A14故最小地址为00000H ，最大地址0000  0111  1111  1111  1111，即07FFFH.
部分译码例子○
用4片6264(8KB) 构成32K× 8(Bits) 存储区，使用部分译码法，分析地址引脚的
使用情况
8K = 23×210 ，所以需要13位地址线进行片内寻址，对应图中每片的A 0~A12。
由于译码器是2 ‐4译码器，只用A 13、A 14用于输入片选信息，剩下的引脚用不到。左图中A 16~A19是一种接法，具体还是取决于地
址的组织方式，只要满足G1高电平G2、G3低电
平即可。（从下面例题的答案看来，在全译码
模式下，貌似不用的脚让其为0 是最好的，所
以用几个与门使其全0 ）
      
 2 0 2 0 - 0 8 - 1 0
{,   3 3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 35 页 ===

全译码和部分译码的综合举例○
存储器芯片与8086系统的接口如图所示，
1)   该存储器子系统的容量是多少？
2）分析RAM芯片和EPROM 芯片的地址范围。
首先分析8086的地址线，A 15~A19全部为1，M'/IO 引脚连接译码器输出低电平，
A12~A14用于输入片选信息。
RAM用A 0~A10共11位地址线片内寻址，大小为2K。
                 译码    ↓ A11EPROM用A 0~A11共12位地址线片内寻址，大小为4K。
RAM 1  1 1 1   1  0 0 1   X 0 0 0   0 0 0 0   0 0 0 0 
   1 1 1 1   1  0 0 1   X 1 1 1   1 1 1 1   1 1 1 1 
X可以是0也可以是1 ，
所以RAM的地址范围是 F9000H~F97FFH  或F9800H~F9FFFH.
片选方式为部分译码，因为A 11未参与译码，存在地址重叠。
                     译码   
EPROM 1  1 1 1   1  1 0 1   0 0 0 0   0 0 0 0   0 0 0 0
1 1 1 1   1  1 0 1   1 1 1 1   1 1 1 1   1 1 1 1
EPROM的地址范围是 FD000H~FDFFFH.
片选方式为全译码，每个存储单元有唯一的、确定的地址。A15~A19在这里没用到，所以会产生25个重叠地址
具体的地址范围与其具体组织方式有关。
      
 2 0 2 0 - 0 8 - 1 0
{,   3 4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 36 页 ===

上图是两个4 位宽度的2114并用组成8 位数据宽度的RAM。 ○
地址线10位和片选信号(CS)' 同时作用于两片芯片。 ○
数据总线的高四位连一片，低四位连一片。○
(WE)'是写控制信号线，也同时作用于两片芯片。 ○
位扩展——数据宽度扩充•三、存储器的数据宽度扩充和字节数扩充
左图是4个8位宽度的27C256 并
用组成32位数据宽度的
EPROM。○
地址线15位和片选信号
(CE)'同时作用于4 片芯片。○
数据总线每8 位数据引脚连一片
芯片。○
(OE)'是输出允许信号线，也
同时作用于4 片芯片。○
      
 2 0 2 0 - 0 8 - 1 0
{,   3 5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 37 页 ===

上图是用4 片16K× 8芯片组成64K× 8RAM。 ○
用两位高位地址线通过2 ‐4译码器用来片选芯片。 ○
WE是写控制信号线，同时连接4 片芯片。 ○
4片的数据线并联到数据总线上。○
地址范围分别是○
_0000H~_3FFFH,  _4000H~_7FFFH,  _8000H~_BFFFH,  _C000H~_FFFFH
最高4位地址线不确定，如果是全译码则很可能置零，如果是部分译码会出现24个
重复地址。
字扩展——字节数的扩展•
左图使用两片32K组成64KEPROM 。 ○
低15位地址线用来片内寻址。○
(CE 0)'和(CE 1)'由高位地址线控制
译码电路进行片选。○
(OE)'是输出允许信号线，同时作
用于2片芯片。○
      
 2 0 2 0 - 0 8 - 1 0
{,   3 6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 38 页 ===

先位扩展，再字扩展○
上图是用8 片1K× 4RAM芯片扩展乘4K×8 存储器。 ○
上4片连接高四位数据总线，下4 片连接低四位数据总线。 ○
地址总线低10位并联8 片，将同列芯片分为1 组，每组是1K× 8，实现位扩展。 ○
用A 10、A 11结合2‐4译码器对4 组进行片选，控制每片的(CE)' 引脚，实现字扩展。 ○
(WE)'同时连接8 片，作为写允许控制线。 ○综合扩展•
      
 2 0 2 0 - 0 8 - 1 0
{,   3 7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 39 页 ===

在查询方式下，CPU通过执行程序不断地区并测试外设的状态，如外设处于“准备
好”状态（输入）设备或“空闲”状态（输出），则CPU 执行输入指令与外设交换
信息。○概念•
CPU从接口中读取状态字①.
CPU检测状态字对应位是否满足“就绪”条件，如不满足，返回前一步读取状态字。②.
如果状态字表明已经处于“就绪”状态，则传送数据。③.条件传送的3 个环节【强调，要会描述，最好结合下图】 •
描述过程时要画图，这样更好得分•
接口部件除了有传送数据的端口外， 必须有状态端口，状态端口也接在数据总线
上。○
对于输入过程来说，当外设将数据准备好时，则是接口的状态端口中的“准备好”标志位置1 ；○
对于输出过程来说，外设取走一个数据之后，接口便将状态端口中的对应标志位置1，表示当前寄存器已经处于“空”的状态，可接收下一个数据。○状态口•
查询方式举例•
假设数据缓冲器的端口地址是82H，状态缓冲器的端口地址是80H ，现在要读取数据
TEST   AL,  01H   ;假设READY 接D0   0000  0001
JZ     STATE
IN     AL, 82HSTATE:  IN     AL, 80H二、查询方式传送数据开关、LED、数码管、步进电机等○只是用简单外设，数据交换不能太频繁。•
输入数据时，使用输入缓冲器，CPU主动，I/O被动，指令：IN  AL,  20H •
输出数据时，使用输出锁存器，CPU主动，I/O被动，指令：OUT  20H,  AL •一、无条件传送方式第五章 微型计算机和外设的数据传输 期末总结
2020年8 月5日1:10
      
 2 0 2 0 - 0 8 - 1 0
{,   3 8ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 40 页 ===

查询式比无条件传送更加可靠，应用场合多。○
不断检测状态字的过程占用CPU大量工作时间，真正传输数据的时间很少。○
没有实时性，查询和做别的工作不能同时进行，需要指定时间收发数据。○
不适合与多个外设进行数据传输。○查询方式优缺点•
在中断方式下，外设具有申请CPU服务的主动权，当输入设备将数据准备好或输出
设备可接收数据时，便可向CPU发送中断请求，使CPU 暂停下目前的工作而和外设
进行一次数据传输。等输入或输出操作完成后，CPU继续进行原来的工作。○
中断方式就是由外设中断CPU的工作，使CPU暂停执行当前程序，而去执行一个数
据输入/ 输出程序，此程序称为中断处理子程序或中断服务子程序，中断子程序执
行完后，CPU又转回来执行原来的程序。○
被外界中断时，程序中下一条指令所在处称为断点。○概念【简述第一段】•
CPU效率高（不用查询）。○
实时性强（外设刚准备好就发送中断请求）。○
与多个外设连接需要中断逻辑电路（8259A ）。 ○
不便调试，中断请求信号随机。○
不够快，单次传输量小。○中断方式的优缺点（优点是相较于查询方式）•
中断优先级，见8259A 章节 •
例子见第六章作业，需要在具体学习数据传输芯片后掌握编程。•三、中断方式传送数据
不管是中断方式还是查询方式，都是CPU执行程序来实现数据传送，以中断为例，每执行一次中断服务子程序都要把标志和断点保存，很费时间，效率不高。○为什么用DMA方式•
在DMA方式下，外设利用专用的接口直接和存储器进行告诉数据传送，而并不经过CPU，也不需要CPU执行指令。○
省去了保护标志和断点等操作，传输速度取决于外设和存储器的速度。○
在利用DMA方式进行数据传输时，DMA控制器需要向CPU发送请求，使CPU让出总
线，把总线的控制权交给DMA控制器（这是一个接口，称为DMAC）。○概念•四、DMA方式（看书自学）
      
 2 0 2 0 - 0 8 - 1 0
{,   3 9ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 41 页 ===

DMAC的功能和DMA传送原理•
具体与CPU的交互过程见第二章“最小模式下的总线保持”
DMA方式从接口往内存传输一个数据块，系统按以下过程动作：•
为使DMA过程正确进行，系统要对DMA 控制器和接口部件预置如下信息：•
（按字节传输）
每个I/O接口部件包含一组寄存器，CPU和外设进行数据传输时，各类信息在接口
中进入不同的寄存器，这些寄存器称为I/O 端口，每个端口都有端口地址。a.
数据端口，对来自CPU和内存的数据或送往CPU和内存的数据起缓冲作用。i.
状态端口，用来存放外部设备或接口部件本身的状态。 ii.
控制端口，用来存放CPU发出的命令，以便控制设备的动作。 iii.有3种端口。 b.什么是I/O端口？通常有哪几类端口？计算机对I/O 端口编址时常采用哪两种方法？
8086系统中采用的是哪一种？1.五、一些问答题（补充）
      
 2 0 2 0 - 0 8 - 1 0
{,   4 0ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 42 页 ===

与存储器统一编址方式i.
I/O端口独立编址方式 ii.有两种编址方式。c.
8086采用I/O端口独立编址方式d.
程序方式：无条件传送方式、条件传送方式（查询方式）a.
中断方式b.CPU和外设之间的数据传送方式有哪几种？2.
见本章a.简述程序查询方式的原理并画出过程的流程图。3.
见本章a.简述中断方式的基本思想。相对于查询方式有何优点？4.
见第二章a.概念解释：中断类型号、中断向量、中断向量表5.
见第二章a.简述8086系统中，可屏蔽硬件中断的响应过程。6.
软件查询、菊花链法、可编程中断控制器（主要）a.解决中断优先级的方法有哪些？7.
见本章a.简述DMA方式的基本思想。和其他两种方式相比，有何本质上的区别？8.
见本章a.简述DMA传输的过程。9.
因为DMA控制器可以控制总线，当它得到总线控制权时，将地址送到地址总线上
以选定对应的存储单元。a.
在控制寄存器中有1 位来指出数据传输方向，当DMA控制器控制总线时，就能判
断进行输入还是进行输出。b.DMAC的地址线为何是双向的？何时输入何时输出？ 10.
      
 2 0 2 0 - 0 8 - 1 0
{,   4 1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 43 页 ===

系统只能进行一个方向的数据传送，只能发送或只能接收。
单工方式：○
同一根传输线既作接收又作发送，虽然数据可以在两个方向上传送，但通信
双方不能同时收发数据。
半双工方式：○
数据的发送和接收分流，分别由两根不同的传输线传送时，通信双方都能在同一时刻进行发送和接收操作。
全双工方式：○串行通信的数据传输模式•
收发双方采用同一个时钟信号来定时（额外的时钟线）。
收发双方以数据块（许多字符）为传输单位，每次发送的数据块称为信息帧。一个信息帧可含有上千个字符。
同步传输必须是连续的，没有信息的地方填空字符。
在信息帧前要加同步字符，用于识别信息帧。在末尾加有校验字段。
同步方式○同步方式和异步方式•一、串行接口和串行通信第六章 串并行通信和接口技术 期末总结
2020年8 月5日16:54
      
 2 0 2 0 - 0 8 - 1 0
{,   4 2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 44 页 ===

发送方在下降沿发送信息，接收方在上升沿接收信息，这样实现中间时刻采
样不易采样出错。
收发双方不采用统一的时钟进行定时，使用本地局部时钟。
收发双方的时钟频率不超过一定的允许范围即可。
异步通信的一个信息帧只包含一个字符（以字符作为传输范围）
异步通信的标准数据格式（一个字符）
一个字符传输开始前，输出线就要处于高电平，称为“标识态”。□
传输开始输出线由1 变为0 ，经历一个数据位宽的传输时间（不是时钟
周期，见下文“波特率因子”），作为起始位。□
之后是5‐8个信息位。 □
最后由可选的奇偶校验位（可高可低）、1,1.5,2 个停止位（高电
平）。□
停止位之后，如果还有数据要传输，则立刻跟进下一个字符的起始位，拉低电平；如果没有多余的数据则将电平拉高保持，进入“标识
态”。□
一帧字符使用起始位进行定位、同步。□
异步方式○
位速率b ：每秒传输的二进制位的个数。记bps ，即Bits/s. ○
波特率B ：每秒传输的离散信号的数目。在计算机系统中位速率就是波特率。 ○
例：若设备每秒传送120帧信息( 字符) ，每帧信息包含： 
1个起始位，7 个数据位，1 个奇偶校验位，1 个停止位，其传送的波特率为多
少？
解：10位/ 帧×120帧/ 秒＝1200位/ 秒＝1200bps
相同的传输率下，同步传输每秒能传输的字符数比异步传输多。串行传输率、位传输率：即波特率，指每秒传输的位数○
通俗的理解就是传输一位数据需要波特率因子个时钟周期。
发送时钟由发送端提前指定，接收时钟由接收端接收。
例：假设波特率因子是16，读取到起始位的电平变化后，接收方会在第8 个
时钟周期上升沿读取，避免干扰电平，即中间时刻采样，然后每隔16个时钟
周期读取每一位的内容。
例：以上内容升级，假设波特率因子是16，读取到起始位的电平变化后，连
续8个时钟周期都读取信号如果都是低电平，则确认是起始位，然后再每隔
数据位的7,8,9 的时钟周期进行采样，并三中取二，降低读取错误的可能。波特率因子K ：发送/ 接收时钟频率与位传输速率之比，通常为1,16,32,64. ○串行通信的一些概念•
      
 2 0 2 0 - 0 8 - 1 0
{,   4 3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 45 页 ===

数据位宽：波特率因子×时钟周期.○
数据速率：1/数据位宽.○
时钟频率：波特率因子×波特率○
有4个寄存器，但只需要两个端口地址。○
控制寄存器和数据输出寄存器是只写的，状态寄存器和数据输入寄存器是制只读
的，用(RD)' 和(WR)' 信号可以加以区分，所以只用两个端口地址。串行接口•
C/D'：控制当前读或写的 是 控制或状态信息 还是 数据信息。读写控制信号○
TxRDY：发送准备好信号，高电平有效，告诉CPU已准备好发送一个字符，可
以接收CPU发来的字符。8251A 接收到CPU的字符之后变为低电平。 既可以用
于查询状态又可以用来作为向 CPU发送的中断请求信号。
TxE：发送器空信号，高电平有效，8251接收到CPU的字符之后变为低电平，
向外设发送完成之后变为高电平，表示此时8251A 的发送器中，并‐ 串转换器
空。
RXRDY：接收器准备好信号，高电平有效，告诉CPU当前8251A 已从外设或调
制解调器接收到一个新字符，正等待CPU取走。CPU 读取字符之后，变为低电
平，8251A 接收到新字符之后又变为高电平。
SYNDET 同步检测信号，高电平有效，仅用于同步模式。 与CPU的收发联络信号（状态信号）○
TxD：CPU送到8251A 的并行数据信号，通过该引脚送往外设。 
RxD：外设送到8251A 的串行数据信号，通过该引脚送往CPU。 与外设之间的数据信号○
与外设之间的联络信号○一些引脚（了解）•二、可编程串行接口芯片8251A 【重点是编程】
      
 2 0 2 0 - 0 8 - 1 0
{,   4 4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 46 页 ===

控制字( 奇地址（高）) ○
模式字( 奇地址（高）) ○
状态字（奇地址（高））○
8251A 和调制解调器连接（远程通信） ○初始化编程( 先模式字再控制字，都是奇地址) •
异步
同步PEN为 0时，
EP无效置1有效
      
 2 0 2 0 - 0 8 - 1 0
{,   4 5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 47 页 ===

8251A 和串行外设连接（打印机、键盘等） ○
TXEN = 1 
(CTS)' = 0 
CPU并行写入8251A ，通过T XD串行发送到外设。 
发送完毕后，T XRDY为1 ，可供查询或提中断。 异步发送○
提中断
或查询
      
 2 0 2 0 - 0 8 - 1 0
{,   4 6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 48 页 ===

RXE = 1 
采样R XD，检测到起始位后，每个数据位的中间时刻对R XD进行采样，直到停
止位为止。过程中进行校验并置标志位
将有效数据并行送入输入缓冲寄存器（数据输入端口满，接收完成），通知
CPU取数据，此时R XRDY置1 ，可供查询或中断。异步接收○
TXEN = 1 
(CTS)' = 0 
发送同步字符1 或2个。 
发送数据块，数据块的字符根据情况决定是否加校验。同步发送○
搜索同步字符，若检测到同步字符，则使SYNDET 引脚输出高电平 
对R XD采样，达到一个字符位数时，R XRDY置1 ，可供查询或中断。
外同步通过8251A 和调制解调器连接。 同步接收（内同步）○
      
 2 0 2 0 - 0 8 - 1 0
{,   4 7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 49 页 ===

例1，设定8251A 为异步工作方式，波特率因子为16，每字符有7 个数据位，偶校
验，有2 位停止位，全双工模式。○
8251A 内部端口地址分配：
40H ‐‐‐ 偶地址 0100  0000
42H‐‐‐奇地址 0100  0010
MOV  AL,  0FAH;11111010B 模式字
OUT  42H, AL
;一般控制字都是37HMOV  AL,  37H;00110111B 控制字
OUT  42H, AL
异步模式下
先写模式字再写控制字
都写在高地址
例2，同步工作方式，2 个同步字符，两个相同同步字符为16H ；采用内同步，
SYNDET 为输出引脚；偶校验，每个字符7 个数据位。使状态寄存器中的3 个出错标
志位复位；使接收器和发送器启动，通知调制解调器CPU 现已准备好进行数据传
输。○
8251A 内部端口地址分配：
40H ‐‐‐ 偶地址 0100  0000
42H‐‐‐奇地址 0100  0010
MOV  AL,  38H;模式字
MOV  42H, AL
MOV  AL,  16H;同步字符写入
OUT  42H,  AL;两个相同的同步字符
OUT  42H, AL
MOV  AL,  97H;或B7H  控制字
OUT  42H, AL
同步模式下
先写模式字，再写同步字符
再写控制字
都写在高地址初始化编程例子•
书上是0 无脑置1 不会错
      
 2 0 2 0 - 0 8 - 1 0
{,   4 8ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 50 页 ===

例3，8251A 作为主机与串行输入设备接口‐ 异步、查询、接收，利用状态字进行编
程的举例○
    先对8251A 进行初始化，然后对状态字进行测试，以便输入字符。本程序段用
来输入80个字符。
     8251A 的控制和状态端口地址为42H，数据输入和输出端口地址为40H。字符
输入后，放在BUFFER 标号所指的内存缓冲区中。
思路：使用查询式输出（主要考察），不断读取R XRDY引脚，对数据进行输入。
补充：对于数据位少于8 位的字符，8251A 默认采取右对齐补0 方式处理。
MOV     AL,  0FAH  ;模式字
OUT     42H, AL
MOV     AL,  37H;控制字
OUT     42H, AL
MOV     DI, 0
MOV     CX, 80
MOV     BX, OFFSET BUFFER
IN      AL,  42H;查询R XRDY
TEST    AL, 02H;00000010B
JZ      BEGIN    
IN      AL, 40H;
MOV     [BX+DI],  AL
INC     DI
IN      AL, 42H
TEST    AL,  38H;检查是否有错
JNZ     ERROR
LOOP    BEGIN
JMP     EXITBEGIN ：
ERROR ：CALL   ERR‐ OUT       
EXIT：     ……
模式字、控制字、同步字符
和状态字都从奇地址读写
只有数据从偶地址端口传输
      
 2 0 2 0 - 0 8 - 1 0
{,   4 9ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 51 页 ===

一次传送一个字长的数据。○
传输速度快、信息率高、用电缆多。○
适合于外部设备与微机之间进行近距离、大量和快速的信息交换○
并行接口与外设连接的示意图○
并行通信：•三、并行通信和并行接口
端口A 对应
1个8位数据输入 锁存器
1个8位数据输出锁存器/ 缓冲器。
端口B 对应
1个8位数据输入缓冲器
1个8位输出锁存器/ 缓冲器。 
端口C 对应
1个8位数据输入缓冲器
1个8位输出锁存器/ 缓冲器。 3个数据端口○
控制端口A 和端口C 的高4位的工作方式和读写操作。 □A组控制电路
控制端口B 和端口C 的低4位的工作方式和读写操作。 □B组控制电路2个控制端口○内部结构•四、可编程并行接口芯片8255A
只有A 口有输入锁存器，其他是输入缓冲器。
      
 2 0 2 0 - 0 8 - 1 0
{,   5 0ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 52 页 ===

错位连接外部引脚和内部编程结构○
用于连接简单外设。
同步传送方式：不需要应答信号。
A、B口作为8 位数据口; 1)
C口作为主机和外设的应答信号：2)
C高4位(或低4 位)设定为输入口, 读入外设状态。 3)
C低4位(或高4 位)设定为输出口, 输出对外设的控制信号。 4)查询式输入/ 输出方式，需要应答信号。其中 
应答信号不固定，可自由定义。
方式0 主要用于同步传送和查询式传送。工作方式0○
A口作为独立的8 位数据输入或输出口。 
B口作为独立的8 位数据输入或输出口。 
输入□
由外设送往8255A 的选通负脉冲，
作用是使8255A 接收外设送来的数据。①(STB)'  选通信号输入端（低电平有效）
当外设送来的数据送入输入端口后， PC5自动变成高电平。指示
输入缓冲器满。可供CPU查询或发送中断请求。②IBF  输入缓冲器满信号（高电平有效）
8255A 送往CPU的中断请求信号。
当INTE为1 且输入缓冲器满时， PC3自动变成高电平，向CPU发
出中断申请，请求CPU读取数据。③INTR 中断请求信号输出端（高电平有效）
④INTE   中断允许控制信号（手动控制）
通过C 口按位置1/0控制字（写入控制口），设置允许/ 不允许
A口：
对PC4置位,  使INTEA=1,  允许提中断C口为A （B）自动提供固定的应答信号（了解）。 工作方式1○初始化编程•
      
 2 0 2 0 - 0 8 - 1 0
{,   5 1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 53 页 ===

对PC4复位,  使INTEA=0,  不允许提中断
B口:
对PC2置位,  使INTEB=1,  允许提中断
对PC2复位,  使INTEB=0,  不允许提中断
输出□
当CPU送来的数据送至8255A 输出缓冲器后，PC7自动变成低电
平。8255A通知外设把数据取走。①(OBF)' 输出缓冲器满信号（低电平有效）
由外设送往8255A ，当输出端口的数据已被外设取走后，外设应
发出低电平信号，作为外设接收数据后的确认信号。此信号
使/OBF信号无效。ACK负脉冲之后可以拉高INTR②(ACK)' 外设响应信号（低电平有效）
在INTE为1的前提下，输出缓冲器空时发出。用来向CPU发出中
断申请，请求CPU再次输出一个新数据。③INTR   中断申请信号（高电平有效）
④INTE   中断允许控制信号
通过控制口对C 口相应位的置位/ 复位设置允许或不允许。
A口，
对PC6置位,  使INTEA=1,  允许提中断
对PC6复位,  使INTEA=0,  不允许提中断
B口，
对PC2置位 , 使INTEB=1,  允许提中断
对PC2复位,  使INTEB=0,  不允许提中断
      
 2 0 2 0 - 0 8 - 1 0
{,   5 2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 54 页 ===

方式1 输入时序
① 当外设数据准备好，将数据送至端口线PA7~PA0
②  外设发出选通信号/STB
(1) 将数据送入8255A数据输入端口内。(2) 使IBF变高，表示输入端口满，供CPU查询。
(3) 如果INTE=1, STB的上升沿使INTR变高，发出中断请求。
③  CPU 读取数据，使/RD信号有效
(1) /RD 的下降沿清除INTR
(2) /RD 的上升沿清除IBF(3) 端口内的数据通过8255A引脚放到数据总线
方式1 输出时序
① CPU输出数据到8255A的数据引脚。
② /WR有效：
(1) 数据写到 8255A的端口，
(2) 使/OBF有效，表示输出端口满，可通知外设取数据。
(3) 清除中断请求信号INTR。
③ 外设取走数据后，发出/ACK信号：
(1) /ACK的下降沿使/OBF变高，
(2) 当INTE=1, /ACK的上升沿使INTR变高，发出中断请求，请求CPU输出
新的数据。
方式选择控制字
A口可以方式0,1,2 ，B口可以方式0,1。 □
C口通常用来配合A 或B，但不要求与A 或B口输入/ 输出方向一致。 □
控制字（最高的地址）○
      
 2 0 2 0 - 0 8 - 1 0
{,   5 3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 55 页 ===

方式选择控制字例子
要求J1  8255A
端口A  方式0 ，输出；
端口B  方式0 ，输入；
端口C 的高四位   输出；
端口C 的低四位   输入；
要求J2  8255A
端口A  方式0 ，输入 
端口B  方式1 ，输出；
端口C 的高四位 输出；
端口C 的低四位 配合端口B 工作
为J1  8255A 设置控制字 □
MOV  AL, 83H
MOV  DX,  00E6H;J1  控制口地址
OUT  DX,  AL;对第1 片8255A 设置方式选择控制字
为J2  8255A 设置控制字 □
MOV  AL,  94H
MOV  DX,  00EEH; J2控制口地址
OUT  DX,  AL;对第2 片8255A 设置方式选择控制字
J1
J2A6A7两位的作用先不管，置1
A8是用来控制译码器有效的低电平有效A3A4A5 用来译码
A1A2用来选芯片内的端口
      
 2 0 2 0 - 0 8 - 1 0
{,   5 4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 56 页 ===

端口C 按位置1/0控制字
端口C 按位置1/0例子
MOV   AL,  0FH      ;对PC7置1 的控制字
MOV   DX,  00E6H   ;控制口地址送DX
OUT   DX,  AL       ;对PC7进行置1 操作
MOV   AL,  06H      ;对PC3置0 的控制字
MOV   DX,  00EEH   ;控制口地址送DX
OUT   DX,  AL       ;对PC3进行置0 操作
      
 2 0 2 0 - 0 8 - 1 0
{,   5 5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 57 页 ===

五、作业综合题（我把老师的答案借鉴之后，重新写了一遍更简洁的）
6‐1
图1   8255A 工作于方式0 作为打印机接口的示意图( 查询方式)
A端口 00E0H ；     B端口 00E2H ；     C端口 00E4H ；     控制口 00E6H
在数据段中定义一个打印字符缓冲区，存放一个以美元符号作为结束标志的字符
串。在数据段中定义一个变量DONE, 初始化时为0 ，全部字符打印完毕后（美元符
号不必打印），将此单元填写FFH. 
请编写汇编语言程序段，采用程序查询方式将此字符串送打印机打印。题目：•
由于是查询式，默认使用方式0 ，A端口作为输出端口，C 口高四位输出
（7‐‐/STB ），低四位输入（1 ‐‐BUSY ）。
整个时序需要靠/STB端口的负脉冲开启，PC 7初始情况下是高电平，因此需要手
动修改两次PC 7，这里用按位置1/0的方法修改。
查询方式的状态信息取自外设的BUSY引脚，即8255 的PC 1脚。
data segment
     OBUFFER DB "The information which is going to output$"   ;打印字符缓冲区
     DONE    DB   0   ;字符串打印结束标志
ends
stack segment
    dw   128  dup(0)
ends
code segment
start:
; set segment registers:
     mov ax, data
     mov ds, ax
     mov es, ax
;****************************************************************   
INIT:  
    MOV AL,81H     ;A口方式0 ，输出，C 口低四位输入，高四位输出
    OUT 0E6H,AL    ;送入控制端口
    MOV AL,0FH     ;使PC7=1分析：•图2 Centronics 并行打印接口时序此处的/ STB是打印机的
引脚，接收到负脉冲则
会接收一个从A口送来的
字符。
81H
      
 2 0 2 0 - 0 8 - 1 0
{,   5 6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 58 页 ===

OUT 0E6H,AL ;初始化结束
LEA DI, OBUFFER   ;D I‐>打印字符缓冲区首单元
LPST:
IN
AL,0E4H  ;读取c 口信息  为状态信息
TEST AL,02H ;测试PC1是否为0 ，（是否空闲）
JNZ LPST ;循环检测，直到打印机空闲
MOV AL, [DI] ;取第一个字符
INC DI
CMP AL, ‘$’ ;将DI指向的字符与 “ $”作比较
JZ  EXIT_ ;相等代表打印结束，跳出。
OUT 0E0H. AL ;不相等则 将该字符送到 A口输出
MOV
AL,0EH
OUT 0E6H,AL ; 拉低PC7
INC AL
OUT 0E6H,AL ; 抬高PC7，给打印机发送选通脉冲，使其接收、打印字符
JMP LPST      ;进行下一次查询、打印
EXIT_:
MOV DONE, 0FFH     ;打印结束 ，置标志
;**************** ****
******************************************
mov ax, 4c00h ; exit to operating system.
int 21h
ends
end start
 2 0 2 0 - 0 8 - 1 0
{,   5 7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 59 页 ===

6‐2
图1  8255Ａ工作于方式１作为用中断方式工作的打印机接口示意图
图2  Centronics并行打印接口时序
A端口 00E0H；    B端口 00E2H；    C端口 00E4H；    控制口 00E6H
题目•
在数据段中定义一个打印字符缓冲区，存放一个以美元符号作为结束标志的字符串。在数据段中定义一个变量DONE,初始化时为0，全部字符打印完毕后（美元符号不必打
印），将此单元填写FFH. 请编写汇编语言程序段，采用中断方式将此字符串送打印机
打印。假设打印机中断类型号是0FH.
提示：•
首字符可直接送到打印机;
遇到美元符号后应能清除中断请求信号，不再引发新的中断申请，中断过程终止。
分析•
A口方式1，输出，C口低四位输出。由于 /ACK用不到，C口高四位不用设为输入。
整个时序需要靠/STB端口的负脉冲开启，PC 0初始情况下是高电平，因此需要手动修改
两次PC 7，这里用按位置1/0的方法修改。图3  8255A方式1 输出时序此处的/ STB是打印机的
引脚，接收到负脉冲则会接收一个从A口送来的
字符。
      
 2 0 2 0 - 0 8 - 1 0
{,   5 8ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 60 页 ===

方式1输出时序
① CPU输出数据到8255A的数据引脚。
② /WR有效：
(1) 数据写到 8255A的端口，(2) 使/OBF有效，表示输出端口满，可通知外设取数据。（本题未用到）
(3) 清除中断请求信号INTR。
③ 外设取走数据后，发出/ACK信号：
(1) /ACK的下降沿使/OBF变高，（本题未用到）
(2) 当INTE=1, /ACK的上升沿使INTR变高，发出中断请求，请求CPU输出新的数据。
提示中的“遇到美元符号清除中断请求信号，指的是执行OUT字符到A口语句后， /WR
有效，会拉低INTR，在此时中断返回就不会再触发下一次新中断。
data segment
    OBUFFER DB "The information which is going to output$"   ;打印字符缓冲区
    DONE     DB   0   ;字符串打印结束标志
ends
    stack segment
        dw   128  dup(0)
ends
code1 segment
    ; set segment registers:
    mov ax, data
    mov ds, ax    mov es, ax
;****************************************************************   
INIT:  
    MOV AL, 0A0H   ; 1010 0000B
    OUT 0E6H,AL    ;将A口设置成方式1 ，方向输出， c口低四位输出
    MOV AL,01H     ; 0000 0001B
    OUT 0E6H,AL    ; PC0=1
    ; 中断向量表初始化
    CLI
    XOR AX, AX     
    MOV DS, AX
    MOV AX, OFFSET INTR
    MOV WORD PTR[003CH], AX   ;中断号0FH
    MOV AX, SEG INTR    MOV WORD PTR[003EH], AX   ;填写中断向量表
    ; 由于要结合8255A 使用中断方式操作，在这里对接收中断的位置1
    MOV AL,0DH         ;0000 1101B
    OUT 0E6H,AL         ;PC6=1 ，允许 8255A 提中断
    STI       
           
    ; 中断向量表初始化完成
    LEA DI, OBUFFER      ;DI‐ >OBUFFER    
    MOV AL, [DI]                       
    INC DI              ;DI++
    OUT 0E0H,AL         ; 将第一个字符送A 口
    MOV AL,00H
    OUT 0E6H,AL ; PC0=0
    INC AL
    OUT 0E6H,AL ; PC0=1 ，产生选通脉冲给打印机，使其接收打印字符；
    ; 取走数据之后，打印机发回/ACK 负脉冲给 8255A, 引发中断申请
;****************************************************************   
    mov ax, 4c00h ; exit to operating system.
      
 2 0 2 0 - 0 8 - 1 0
{,   5 9ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 61 页 ===

    int 21h     
code1 ends
CODE2 SEGMENT     
INTCODE   PROC   FAR      ; 中断服务子程序 远过程
INTR:  
    MOV  AL, [DI]
    CMP  AL,‘$’     ;比较DI指向的待打印字符和“$ ”
    JZ   EXIT_       ;若两者相等，则代表打印完毕，跳出
    INC  DI          ;若两者不等，DI增量
    OUT  0E0H,AL      ;将当前字符送给A 口
    MOV AL,00H       
    OUT 0E6H,AL       ;PC0=0
    INC AL
    OUT 0E6H,AL       ;PC0=1, 产生选通脉冲给打印机，使其接收打印字符；
    ; 取走数据之后，打印机发回/ACK 负脉冲给 8255A, 引发下一次中断申请中断插队了。
    ; 这里产生了很多层中断嵌套，因为之前的中断还没结束，就被新的
    JMP  RETURN_      ;中断返回，返回所有嵌套的中断。
EXIT_:  
    OUT 0E0H, AL      ;将当前字符写入8255A 端口
    ; 这句OUT的意义不是传数据，而是 拉低I NTR， 不在继续出发中断申请。
    MOV DONE, 0FFH    ;遇见$ 置打印结束标志 0ffh
RETURN_:   
    IRET              ; 中断返回
INTCODE ENDP
CODE2  ENDS
      
 2 0 2 0 - 0 8 - 1 0
{,   6 0ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 62 页 ===

题目•
8086系统中利用8251A 作为和某串行输出外设的接口。假设将要传送到此外设
的数据已经存放到数据段的发送数据缓冲区中。设8251A 工作于异步方式，波特
率为2400bps ，收发时钟频率为153.6KHz ，异步数据格式为：8位数据位、1位
停止位、偶校验，允许接收，允许发送，全部错误标志复位。8251 端口地址为
20H，22H
请编写8251A 的初始化程序和以查询方式通过8251A 往外设发送数据的汇编语言
程序段。
分析•
波特率因子 K = 153600/2400=64
DATA SEGMENT
     BUFFER  DB 'ABCDEFG','$'; 要输出的内容
     COUNT  DW $‐BUFFER‐1 ;字符数量
     PKEY DB "PRESS ANY KEY...$"
ENDS
CODE SEGMENT
START:; SET SEGMENT  REGISTERS:
     MOV  AX, DATA
     MOV  DS, AX
     MOV  ES, AX
     ; ADD YOUR CODE HERE
        
     ;====== 初始化======
     ;波特率因子 K = 153600/2400=64
     MOV  AL,  08FH; 模式字
     OUT  22H, AL
     MOV  AL, 038H; 控制字
     OUT  22H, AL
     ;==== 初始化完成 ====
      
  MOV  CX,  COUNT; 计数
     MOV  DI, 0
         ;==开始查询式输出==
    CHA:    ; 查询，等待数据输出端口空
     IN   AL, 22H
     TEST  AL,  01H     ;判断数据输出缓冲口是否为空（1 ）
     JZ   CHA          ;不空就开始输出
    
     MOV  AL,  BUFFER[DI]; 把第DI个字符传到AL
     OUT  20H, AL
     INC  DI
     LOOP CHA
     ;==完成查询式输出==
    
     LEA  DX, PKEY
     MOV  AH, 9
     INT  21H         ; OUTPU
T STRING AT DS:DX
    
     ; WAIT FOR ANY KEY....     
     MOV  AH, 1
     INT  21H
    
     MOV  AX, 4C00H ; EXIT TO OPERATING  SYSTEM.
     INT  21H    
ENDS
END  START ; SET ENTRY POINT AND STOP THE ASSEMBLER.8‐1
关于取长度
取数组长度：     $  ‐<数组标号>
取字符串长度： $ ‐<串标号> ‐1
      
 2 0 2 0 - 0 8 - 1 0
{,   6 1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 63 页 ===

MOV  DX,52H     ; 控制、状态端口奇地址是 52H
; 同步模式，数据位 5位，偶校验，外同步， 2个同步字符                               MOV  AL,80H
OUT  DX,  AL
MOV  AL,  5AH     ; 写入同步字符5AH
OUT  DX,  AL
; 搜索同步字符，不进行内部复位，不使 /RTS输出低电平
; 不使TxD成为低电平               ; 使状态寄存器中的3 个出错标志位复位
                ; 接收允许，使/DTR输出低电平，发送允许MOV  AL,  97H
OUT  DX,  AL
MOV  DI,  3000H      ; 接收数据的首地址
MOV  CX,  1000H      ; 要接收的字符的长度; 初始化8251A
; 查询方式同步接收
IN AL, DX
TEST AL, 02H     ; 判断D1 位RxRDY是否为1
; 等到其为0 才能传输数据JZ STT          ; RxRDY为1表示，数据输入缓冲器满；STT:      MOV DX,  52H     ; 读取状态端口的状态
MOV  DX,  50H     ; 传送数据，数据端口偶地址是 50H
IN   AL,  DX      ; 把数据端口上的字符读取到 AL 
MOV  [DI], AL      ; 把字符送到 DI地址所对应的位置
INC  DI          ; DI加一
LOOP STT         ; CX减一，循环直到CX为 0为止
HLT              ; 停机8‐2 读程序加注释
      
 2 0 2 0 - 0 8 - 1 0
{,   6 2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 64 页 ===

一、8259A 的功能、内部编程结构
8259A 的初始化流程 •二、8259A 的初始化命令字和初始化流程
一般是单片方式
需要设置ICW 1偶，ICW 2奇，ICW 4奇
多数情况下采用边沿触发普通嵌套，非缓冲方式
非自动结束方式第七章 中断控制器 期末总结
2020年8 月7日16:39
      
 2 0 2 0 - 0 8 - 1 0
{,   6 3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 65 页 ===

ICW 1（偶地址）○
ICW 2（奇地址）○
ICW 4（奇地址）○
8259A 的命令字 •
    IBM PC系统中（8 位）只有一片8259A ，可接受外部8 个中断源的中断申请。
在I/O地址中，分配8259A 的端口地址为20H和21H，
边沿触发、缓冲连接、
中断结束采用 一般中断结束命令（非自动）、
中断优先级采用 普通全嵌套方式（没写“特殊”二字就是一般方式）、
8个中断源的中断类型号分别为08H~0FH 。（前五位都是00001 ）初始化为：
MOV  DX，20H
MOV  AL，13H ;0001 0011B
OUT  DX，AL  ; 写入ICW 1
MOV  DX，21H
MOV  AL，08H ;0000 1000B
OUT  DX，AL  ; 写入ICW 2
MOV  AL，0DH ; 0000 1101B
OUT  DX，AL  ; 写入ICW 4
用或不用DX寄存端口地址都可以，可以直接IN/OUT  21H8259A 初始化命令字例子 •标志码
后三位直接填0
EOI：中断结束
      
 2 0 2 0 - 0 8 - 1 0
{,   6 4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 66 页 ===

OCW 1中断屏蔽字（奇地址）○
可以直接设置允许与屏蔽，也可以新增允许与屏蔽
设置中断屏蔽字
MOV  DX, 22H
MOV  AL,  92H  ; 10010010B  屏蔽147允许02356
OUT  DX,  AL  ; 写入IMR
新增中断允许
IN   AL,  22H  ; 读取原IMR的允许与屏蔽状态
AND  AL,  FBH ;  1111  1011B 新增允许2
OUT  22H,  AL ; 写入IMR
新增中断屏蔽
IN   AL,  22H  ; 读取原IMR的允许与屏蔽状态
AND  AL,  04H ;  0000  0100B 新增屏蔽2
OUT  22H,  AL ; 写入IMR假设8259A 的奇地址为22H
发中断结束命令，使ISR（当前中断服 务寄存器）中 对应IS n位复位。 
设置优先级循环方式（不用管）。
中断结束命令位于中断处理子程序的关中断步骤和中断返回步骤之间。OCW 2中断结束命令（偶地址） ○
只记这两个就行
8259A 的操作命令字（操作命令字需要关中断设置） •
此方式下，后三位无关
      
 2 0 2 0 - 0 8 - 1 0
{,   6 5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 67 页 ===

例子
IBM PC系统中,  8259A的偶地址端口地址为 20H;
系统初始化 8259A 时设置它为非自动清 0 方式。8259A的某个中断申请引
脚上连接了某个外设的中断请求，则此设备对应的中断服务子程序中，在中
断返回前，必须发中断结束命令EOI给8259A。
分析：非自动清零模式，ICW 4中D 1位为0，没说具体的某个引脚，所以全复
位，发送一般中断结束命令，后三位为无关位，按0处理。
intsub  PROC
         PUSH AX
MOV   AL,  20H           ;将中断结束命令字20H  送AL          ……
POP AX         OUT   20H,  AL           ;写入OCW 2中
         IRET                   ;中断返回
intsub  ENDP
注：两个20H不一样
开中断（可被新的中断中断）①.
保护现场②.
其他操作③.
恢复数据④.
关中断⑤.
发送中断结束命令⑥.
中断返回⑦.重复一遍中断处理子程序的流程•三、中断全嵌套
      
 2 0 2 0 - 0 8 - 1 0
{,   6 6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 68 页 ===

一个8259 A主片上可以连接8个从片，最多允许64个中断请求线与外界相连。 •
主从式系统的中断响应过程•
四、主从式中断系统
五、编程题完善    6‐2
图1  8255Ａ工作于方式１作为用中断方式工作的打印机接口示意图
图2  Centronics并行打印接口时序
A端口 00E0H；    B端口 00E2H；    C端口 00E4H；    控制口 00E6H
题目•
在数据段中定义一个打印字符缓冲区，存放一个以美元符号作为结束标志的字符串。在数据段中定义一个变量DONE,初始化时为0，全部字符打印完毕后（美元符号不必打
印），将此单元填写FFH. 请编写汇编语言程序段，采用中断方式将此字符串送打印机
打印。假设打印机中断类型号是0FH，8259A的偶地址80H，奇地址82H.
提示：•
首字符可直接送到打印机;遇到美元符号后应能清除中断请求信号，不再引发新的中断申请，中断过程终止。分析•图3  8255A方式1 输出时序此处的/ STB是打印机的
引脚，接收到负脉冲则会接收一个从A口送来的
字符。
      
 2 0 2 0 - 0 8 - 1 0
{,   6 7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 69 页 ===

A口方式1，输出，C口低四位输出。由于 /ACK用不到，C口高四位不用设为输入。
整个时序需要靠/STB端口的负脉冲开启，PC 0初始情况下是高电平，因此需要手动修改
两次PC 7，这里用按位置1/0的方法修改。
方式1输出时序
① CPU输出数据到8255A的数据引脚。
② /WR有效：
(1) 数据写到 8255A的端口，(2) 使/OBF有效，表示输出端口满 ，可通知外设取数据。（本题未用到）
(3) 清除中断请求信号INTR。
③ 外设取走数据后，发出/ACK信号：
(1) /ACK的下降沿使/OBF变高 ，（本
题未用到）
(2)当INTE=1, 
/ACK的上升沿使INTR变高，发出中断请求，请求CPU输出新的数据。
提示中的“遇到美元符号清除中断请求信号，指的是执行OUT字符到A口语句后， /WR
有效，会拉低INTR，在此时中断返回就不会再触发下一次新中断。8259A的初始化采用最普遍情况，单片，边沿触发，普通全嵌套，非缓冲，非自动中断
结束，允许IR
3，使用一般中断结束命令。
data segment
  OBUFFER DB "The information which is going to output$"   ;打印字符缓冲区
  DONE     DB    0   ;字符串打印结束标志
ends
     stack segment
     dw   128   dup(0)
endscode1 segment     ; set segment registers:
     mov ax, data
     mov ds, ax
     mov es, ax
;****************************************************************   
INIT:  
     MOV AL, 0A0H   ; 1010 0000B
  OUT 0E6H,AL    ;将A口设置成方式1 ，方向输出，c 口低四位输出
     MOV AL,01H      ; 0000 0001B
     OUT 0E6H,AL    ; PC0=1   ;8255A 初始化
   ;8259A 初始化
 MOV  DX,  80H      ;将偶地址取到DX中
  MOV  AL, 13H 
 OUT  DX,  AL       ;写入ICW1
     MOV  AL, 08H  MOV DX, 82H
 OUT  DX,  AL       ;写入ICW2
  MOV
 AL, 01H   
 OUT  DX,  AL       ;写入ICW4
 2 0 2 0 - 0 8 - 1 0
{,   6 8ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 70 页 ===

  ;中断向量表初始化、8255 提中断、 8259A 操作命令字
     CLI
     XOR AX,
AX   
     MOV DS, AX
     MOV AX, OFFSET INTR
  MOV WORD PTR[003CH], AX   ;中断号0FH
     MOV AX, SEG INTR
  MOV WORD PTR[003EH], AX   ;填写中断向量表
  ;由于要结合8255A 使用中断方式操作，在这里对接收中断的位置 1
     MOV AL,0DH          ;0000 1101B
  OUT 0E6H,AL        ;PC6=1 ，允许8255A 提中断
 IN   AL,  82H  ; 读取原IMR的允许与屏蔽状态
 AND  AL,  F7H  ; 1111  0111B 新增允许IR3
 OUT  8H,  AL ; 写入IMR
     STI    
  ;中断向量表初始化完成
 ; 8255A 的有关提中断的设定完成
     LEA DI, OBUFFER       ;DI‐>OBUFFER   
     MOV AL, [DI]      
     INC
DI       ;DI++
  OUT 0E0H,AL     ;将第一个字符送A 口
     MOV AL,00H
     OUT 0E6H,AL ; PC0=0
     INC AL
  OUT 0E6H,AL ; PC0=1 ，产生选通脉冲给打印机，使其接收打印字符；
     ;取走数据之后，打印机发回/ACK 负脉冲给 8255A, 引发中断申请
;****************************************************************   
     mov ax, 4c00h ; exit to operating system.
     int 21h   
code1 ends
CODE2 SEGMENT     
INTCODE   PROC    FAR     ;中断服务子程序 远过程
INTR:  
     MOV   AL, [DI]
  CMP  AL, ‘$’       ;比较DI指向的待打印字符和“$”
  JZ   EXIT_       ;若两者相等，则代表打印完毕，跳出
  INC  DI      ;若两者不等，DI增量
  OUT  0E0H,AL       ;将当前字符送给A 口
     MOV AL,00H    
     OUT 0E6H,AL        ;PC0=0
     INC AL
  OUT 0E6H,AL       ;PC0=1, 产生选通脉冲给打 印机，使其接收打印 字符；
  ;取走数据之后，打印机发回
/ACK负脉冲给 8255A, 引发下一次中断申请中断插队了。
  ;这里产生了很多层中断嵌套，因为之前的中断还没结束，就被新的
     JMP  RETURN_       ;中断返回，返回所有嵌套的中断。
EXIT_:  
  OUT 0E0H, AL       ;将当前字符写入8255A 端口
  ;这句OUT的意义不是传数据，而是拉低 INTR，不在继续出发中断申请。
     MOV DONE, 0FFH    ;遇见$ 置打印结束标志 0ffh
RETURN_: ; 8259A 的操作命令字的设定完成
 MOV  AL,  20H    ;将中断结束命令字20H  送AL
  IRET      ;中断返回
INTCODE ENDPCODE2  ENDS
其实所有初始化工作全都在关中断期间完成也是可取的，见第九章作业题。总结：8255A 允许提中断（C端口按位）和8259A操作命令字的设定需要关中断进行。 OUT  80H,  AL       ;写入OCW2 中
 2 0 2 0 - 0 8 - 1 0
{,   6 9ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 71 页 ===

8253为2.6MHz○
8254为10MHz○频率•
编程结构•
8253是16位计数器，最大值是0 （65536 ）计数范围是0000~FFFFH •
在减法计数中，假设CLK端是T ，初值是N ，则OUT端T X = NT •
如果用加法计数器，T X= (2计数部件位数‐N)T •一、8253/8254 的编程结构和工作原理
写入控制字后，OUT进入初始状态，在不同模式下不同。
写入初值之后，经过一个时钟上升沿一个时钟下降沿之后，计数执行部件CE
得到初值，开始计数。
在每个时钟周期的下降沿，计数器做减一计数。
GATE保持高电平的情况下写入初值就立即启动（软启动），且保持在GATE=1 的情况下进行计数，GATE=0 则计数停止。
用于模式0 和模式4 。 GATE高电平有效□
由外部电路为GATE提供一个上升沿（硬启动），计数器从初值开始计数，之后GATE的状态不再影响计数。GATE沿触发□门控GATE具有启动、进行、停止控制作用共同点○工作模式归类•二、工作模式与初始化编程一片8253 拥有4 个连续的偶地
址，分别对应通道0 、通道1 、
通道2 ，控制寄存器第九章 计数器/定时器 期末总结
2020年8 月8日0:21
      
 2 0 2 0 - 0 8 - 1 0
{,   7 0ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 72 页 ===

用于模式1 和模式5 。 
软启动+ 硬启动，保持在GATE=1 的情况下进行计数，GATE=0 则计
数停止。
用于模式2 和模式3 。 GATE电平触发和沿触发结合。□
/WR端会写入两次，写入控制字后OUT端立刻变为低电平，写入初值N 后经过
一个时钟上升沿和一个时钟下降沿后从初值N 开始计数。①.
模式0 使用电平触发，开始计数后，在每个时钟下降沿计数器的值减1 ，OUT
维持低电平；在计数器的值由1 变0的时刻，OUT 端电平拉高并保持， 常作为
中断请求信号。②.
计数期间，GATE应保持高电平，如果被拉低，则计数器中的值保持，OUT维
持低电平，直到GATE重新变为高电平，继续计数。③.
计数期间，如果再次写入新的初值，则写入新的初值之后，经过一个时钟上
升沿和一个时钟下降沿后从新的初值开始计数。④.
模式0 是一次性计数，除非写入新的初值，不会重复计数。 ⑤.模式0 ——计数结束产生中断 ○
/WR端会写入两次，写入控制字后OUT端立刻变为高电平，写入初值N 后经过
一个时钟上升沿和一个时钟下降沿后从初值N 开始计数。①.
模式4 使用电平触发，输出单一的负脉冲。开始计数后，在每个时钟下降沿
计数器的值减1 ，OUT维持高电平；在计数器的值由1 变0的时刻，OUT 端电平
拉低，1 个时钟周期后拉高，输出一个负脉冲。拉高后计数器的值恢复为初
值，进行重复计数。②.
计数期间，GATE应保持高电平，如果被拉低，则计数器中的值保持，OUT维
持低电平，直到GATE重新变为高电平，继续计数。③.
计数期间，如果再次写入新的初值，则写入新的初值之后，经过一个时钟上升沿和一个时钟下降沿后从新的初值开始计数。④.
模式4 是循环计数，输出内容高低电平比为N ∶1。 ⑤.模式4 ——软件出发的选通信号发生器 ○
      
 2 0 2 0 - 0 8 - 1 0
{,   7 1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 73 页 ===

模式1 使用GATE 上升沿触发 ①.
写入模式控制字后，OUT变为高电平。②.
写入初值之后，待GATE出现上升沿（正脉冲），在随后的CLK下降沿从初值
开始计数，OUT拉低。③.
每个CLK下降沿计数器的值减1 。在从1 变0的下降沿OUT拉高。 ④.
再次出现GATE上升沿时重新从初值开始计数。⑤.
GATE上升沿触发计数之后，GATE可以保持，可以变低电平，但再次触发还
需要拉低在拉高出现上升沿。⑥.
如果GATE接入其他可重复计数的模式的OUT信号输入，也只能计数一次。⑦.模式1 ——可编程的单稳态触发器 ○
模式5 使用GATE 上升沿触发 ①.
写入模式控制字后，OUT变为高电平。②.
写入初值之后，待GATE出现上升沿（正脉冲），在随后的CLK下降沿从初值
开始计数，OUT保持高电平。③.
每个CLK下降沿计数器的值减1 。在从1 变0的下降沿OUT拉低一周期再拉高，
输出一个负脉冲。④.
再次出现GATE上升沿时重新从初值开始计数。⑤.
GATE上升沿触发计数之后，GATE可以保持，可以变低电平，但再次触发还
需要拉低在拉高出现上升沿。⑥.
如果GATE接入其他可重复计数的模式的OUT信号输入，也只能计数一次。⑦.模式5 ——硬件触发的选通信号发生器 ○
      
 2 0 2 0 - 0 8 - 1 0
{,   7 2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 74 页 ===

模式2 既可以用 电平触发、也可以用 上升沿触发 。 ①.
写入模式控制字后，OUT变为高电平。②.
写入初值之后，在GATE=1 的情况下，在随后的CLK下降沿从初值开始计数，
OUT保持高电平。③.
每个时钟周期下降沿计数器减1 ，在从 2变为1的下降沿拉低OUT1 个时钟周期
然后再拉高重新从初值开始计数， 没有0.④.
计数期间，GATE应保持高电平，如果被拉低，则停止计数，拉高之后出现上
升沿，重新从初值开始计数。这一点可以用来 同步多个计数器。⑤.
模式2 是循环计数，高低电平比是(N ‐1)∶1，可用来驱动别的模式的计数器。 ⑥.模式2 ——分频器 ○
模式3 既可以用 电平触发、也可以用 上升沿触发 。 ①.
写入模式控制字后，OUT变为高电平。②.
写入初值之后，在GATE=1 的情况下，在随后的CLK下降沿从初值开始计数，
OUT保持高电平。③.
每个时钟周期下降沿计数器减1 ，在OUT的后半周期拉低电平，在1 变N的时钟
下降沿拉高，循环计数， 没有0.④.
OUT输出电平是先高后底，当初值为偶数时，高低电平比是1 ∶1，当初值是
奇数时，高电平时间比低电平多一个时钟周期。⑤.
计数初值为 0 （65536），输出的方波频率为 18.2 HZ，周期为1/ 18.2 =55ms ⑥.模式3 ——方波发生器 ○
      
 2 0 2 0 - 0 8 - 1 0
{,   7 3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 75 页 ===

模式设置控制字○
模式控制字写入控制寄存器（最高的地址）。○8253/8254 的初始化编程 •
写初值的地址要与其通道相对应。○
0‐255只写低八位，256‐65535 先写第八位后写高八位。 
BCD码：要在数字后面写上H。
非BCD：直接写数字就按十进制（推荐），写上H就是二进制初值用BCD码还是二进制格式写，在模式字中设定。○定义初值•
假设8253的地址为B0H,  B2H,  B4H,  B6H。 ○
例1○
MOV  AL，13H   ; 0001  0011B
OUT  0B6H，AL  ; 写入控制寄存器地址
MOV  AL， 50H   ; 计数初值
OUT  0B0H，AL  ; 写入计数器0计数器0 ，方式1 ，N=50
例2○
MOV   AL, 77H   ; 0111  0111B ；
OUT   0B6H, AL ; 控制寄存器地址
MOV  AX,  1250H
OUT   0B2H, AL ; 写入计数器1,  低字节
MOV   AL,  AH          ;  这句是按书例写的 
OUT   0B2H, AL ; 写高字节计数器1 ，方式3 ，N=1250
例3○
MOV  AL,  90H  ; 1001  0000B ；
OUT  0B6H, AL; 控制寄存器地址
MOV  AL,  124 ; 或MOV  AL,  7CH
OUT  0B4H, AL; 写入计数器2计数器2 ，方式0 ，N=124
例4○
MOV  AL, B0H ; 1011 0000B
OUT  0B6H, AL; 控制寄存器地址
MOV  AX, 1280 计数器2 ，方式0 ，N=1280例子•模式选择000~101 1：BCD码
0：非BCD
   直接写数字是十进制
   写上H 则是二进制01：只写低八
11：先低八后高八00——计数器0/通道001——计数器1/通道110——计数器2/通道2
11——控制 /状态寄存器，仅8254
      
 2 0 2 0 - 0 8 - 1 0
{,   7 4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 76 页 ===

OUT  0B4H, AL; 写入计数器2,  低字节
MOV  AL, AH 
OUT  0B4H, AL; 写高字节
8253的端口地址：40H、41H、42H和43H，
输入时钟都为1.193MHz例1
问题1○
8259A 右边连着8088, 硬件为8088提供中断，有频率地执行中断子程序。
GATE接+5V，处于恒开启状态，当装入一个初值后，即开始计数。
计数初值为 0 （65536 ），输出的频率为 18.2  HZ，周期为1/18.2  =55ms 。
输出OUT 0 接于  8259  的中断请求线 IRQ 0 上，每隔   55ms  产生一次 IRQ 0中断
请求。日时钟子程序的中断号是08H。8259A 的地址是20H，21H。
IRQ 0中断被响应时，系统进入 BIOS  日时钟中断子程序，产生日时钟计数。
请分析通道0 的工作模式，完成通道0 的初始化和初值填写，8259 的初始化，中断
子程序的中断结束命令。
分析：
通道0 工作在模式3 ——方波发生器，每55ms 输出一个上升沿，故8259A 为边沿触
发。每55ms产生一次中断用于执行日时钟子程序。; 初始化通道0
MOV  AL, 36H ; 00110110B
OUT  43H, AL
MOV  AL,  0   ;初值是0 代表65536 ，所以要先写低八再写高八
OUT  40H, AL 
OUT  40H, AL
;初始化8259A
MOV  AL, 13H ; 00010011B
OUT  20H, AL ; ICW1
MOV  AL, 08H ; 
OUT  21H, AL ; ICW2
三、8253的应用例子
      
 2 0 2 0 - 0 8 - 1 0
{,   7 5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 77 页 ===

MOV  AL, 00H
OUT  21H, AL ; ICW4
MOV  AL, 0FEH
OUT  21H, AL ; OCW1
; 中断子程序的中断结束命令
……
MOV  AL,  20H  ; 一般中断结束命令
OUT  20H, AL ; OCW2
IRETCLOCK PROC FAR
CLOCK ENDP
问题2○
8253OUT1 提供对DRAM的刷新时间间隔15.0857 μs，每次刷新需要OUT 1输出一个
负脉冲。 GATE恒接 +5V。
请分析通道1 的工作模式，计算初值，完成通道1 的初始化和初值填写。
分析：
通道1 工作在模式2 ——分频器，周期性输出负脉冲。
OUT1频率=1/  15.0857   μs=  66.287 KHZ 
初值N=1.1931816  MHZ  / 66.287 KHZ  = 18
; 初始化通道1
MOV  AL, 54H ; 0101 0100B
OUT  43H, AL
MOV  AL, 18
OUT  41, AL
问题3○
使用通道2 控制扬声器发声，工作在方式3 下，预置初值为 533H，GATE2  受系统
并行接口芯片8255  PB0 的控制；OUT2   受系统并行接口芯片8255  PB1 的控
制。8255的地址为60H，61H，62H，63H 
请完成通道2 和8255的初始化。
; 通道2 初始化
MOV  AL, B6H ; 10110110B
OUT  43H, AL
MOV  AX, 0533H
OUT  42H, AL
MOV  AL, AH
OUT  42H, AL 
; 8255初始化
MOV  AL, 80H ; 10000000B
OUT  63H,   AL ；方 式 0，B输出
OUT  61H,  03H  ; 00000011B  对B口最低2 位置1
      
 2 0 2 0 - 0 8 - 1 0
{,   7 6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 78 页 ===

设三个通道的初值分别为L,M,N ，时钟的频率为F 。
8253的地址为70H‐ 76H
问题1○
求三个通道的CLK,GATE,OUT 的表达式，推导出CONVERT 引脚输出的信号，并画出
波形。
假设M=6，N=2，L=5（画图用）
T(CLK 0) = T(CLK 2) = 1/F 方波
T(CLK 1) = T(OUT 2) = N·T(CLK 2) = N/F（取反）模式3 ——方波
T(GATE 0) = T(OUT 1) = M·N·T(CLK 1) = M·N/F（取反）模式1 ——触发器
GATE 1和GATE 2由手动开关控制，接通变为高电平，不接通则是低电平。
T(CONVERT)  = T(OUT 0) = L·T(CLK 0) = L/F（取反）模式2:——分频器例2•
问题2○
手动开关闭合一次后，采样持续多长时间？持续时间为GATE
0高点平时间，T(OUT 1)=M·N/F
问题3○
在问题2 的持续时间内，采样的间隔为？采样的频率为？
T(OUT 0)=L/F ，f(OUT 0)=F/L
问题4○
8255A 什么情况下发出中断申请？8255A 对应的中断服务子程序，主要工作是什
么？描述各器件的初始化程序要做的工作。8255A 工作于工作方式1 （因为用了C 端口的4,5位）
由外设送往8255A 的选通负脉冲，作用是使8255A 接收外设送来的数据。PC
4是(STB)'  选通信号输入端（低电平有效）
5  4   3  2  1  5   4  3  2  1   5  4  4   5
  6      5       4      3      2       1     FF   6
      
 2 0 2 0 - 0 8 - 1 0
{,   7 7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 79 页 ===

当外设送来的数据送入输入端口后， PC5自动变成高电平。指示输入缓冲器
满。可供CPU查询或发送中断请求。PC5是IBF  输入缓冲器满信号（高电平有效）
执行中断子程序之前，采样到的信号锁存在数据端口的缓冲区，中断子程序要讲
这部分数据读取并进行记录及其他处理。
问题5○
对三个通道初始化。
        MOV        AL, 14H;   00 01  010 0
        OUT        76H, AL; 将计数器0 设置为模式2
        MOV        AL, LCNT
        OUT        70H, AL; 对计数器0 设置计数初始值L(二进制)
        
        MOV        AL, 73H; 01 11   001  1
        OUT        76H, AL; 将计数器1 设置为模式1
        MOV        AX, MCNT
        OUT        72H, AL
        MOV        AL, AH
        OUT        72H, AL; 对计数器1 设置初始值M(BCD 码)
        
        MOV        AL, 96H; 10   01 011  0
        OUT        76H, AL; 将计数器2 设置为模式3
        MOV        AL, NCNT
        OUT        74H, AL; 对计数器2 设置初始值 N( 二进制)
      
 2 0 2 0 - 0 8 - 1 0
{,   7 8ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 80 页 ===

四、综合作业题（直接贴上老师的答案，是一个很好的综合样例，初始化和中断子程序）
7‐1
图1
图1是由8255A 、8253、8259A 组成的一个8086系统的示意图（CPU8086 在左侧未画出）。在
8255A 的A口连接了4 个开关，开关的状态( 闭合为低电平；断开为高电平) 由A口输入，8253
的通道2 ，产生周期性的20ms定时信号，每隔20ms 检测1 次8255A  A口所连开关的状态，并且
将开关状态反映到B 口所连的LED亮灭，比如当开关K0闭合时，点亮PB0 所接LED，当K0断开
时时PB0所接LED灭。初始时所有LED灭。PB4~PB7 所接LED始终灭。（允许同时闭合多个开
关）
提示：当8253通道2 定时时间到，通过8259A  IR2向CPU提出中断申请，在中断服务子程序当
中完成8255A 所连接的4 个开关的状态检测和处理。
暂不用分析实际连线地址。假设8253的内部端口地址为    B0H     B2H     B4H     B6H
8259A的内部端口地址为   C0H     C2H    
8255A的内部端口地址为   D0H     D2H    D4H     D6H
1.完成初始化，关键语句后面加注释
（
1） 完成对8255 的初始化编程
（2） 确定8253 通道2 工作方式，计算初值；完成对8253的初始化编程。 
1）请简单说明，如果初始化8259A, 需要初始化哪几个寄存器，在初始化当中设置什
么； 2）完成对8259A 的初始化编程,  
3）开放IR2、IR1上的中断申请（3） 假设系统中只有单片8259A,  非缓冲， 8个引脚上的中断类型码从08H开始，普通全嵌
套方式，一般的中断结束方式。
（4）填写中断向量表的相关区域
（5）必须的其他的初始化等 
      
 2 0 2 0 - 0 8 - 1 0
{,   7 9ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 81 页 ===

2.中断服务子程序
（1）请简单说明：中断服务子程序要做的工作。并画出流程图。
（2）IR2中断服务子程序的关键代码。
1）、计数器2 工作在方式3 （工作方式2 也可以），初值N=1000 ；
2）、8259A ：ICW1设置为中断请求为沿触发有效，单片；
            ICW2  写入08H，初始化中断类型号；
            ICW4  设置为普通全嵌套，非缓冲，非自动结束中断；
3）、中断子程的工作：
每隔20ms进入一次IR2中断服务子程序，读取一次A 口状态，屏蔽高四位（P4‐ P7灯始终灭）
后，发送给B 口，使对应的led亮或者灭，中断返回。
stack segment
     dw    128  dup(0)
ends
code1 segment
CLI            ;   关中断start:
     ;************************************************  
     ;对8255A初始化，假设 8255A的内部端口地址为 D0H     D2H     D4H     D6H
     MOV  AL, 90H   ;1001 0000B: 8255A设置为A口输入， B口输出，方式 0
     OUT  0D6H, AL   ;发送给控制端口
    
     MOV  AL,0FFH
     OUT  0D2H,AL  ;初始化LED 灯全灭，写入 B口 
     ;************************************************
      ;对8253初始化，假设8253的内部端口地址为B0H     B2H     B4H     B6H
      MOV AL,0B7H;1011  0111B 选择计数器 2，先低 8，后高 8，方式 3，BCD码
      OUT 0B6H,AL
           MOV AX,1000H
      OUT 0B4H,AL
      MOV AL,AH
      OUT 0B4H,AL   ;将计数初值设置为 N=1000 ，送入计数器2  
      ;************************************************
      ;对8259A进行初始化，假设 8259A的内部端口地址为C0H     C2H   
      MOV AL,13H;0001  0011B:单片，请求信号为沿触发
      OUT 0C0H,AL;  写入ICW1
     
      MOV AL,08H
      OUT 0C2H,AL; 初始化中断类型号
     
      MOV AL,01H; 普通全嵌套，非缓冲，非自动结束中断
      OUT 0C2H,AL; 写入ICW4
     
      IN AL,0C2H
      AND AL,0F9H;1111  1001B开放IR1和 IR2的中断申请
     OUT 0C 
2H,AL   
      ;************************************************
      ;填写中断向量表
      XOR AX,AX
      MOV DS,AX
      MOV AX, OFFSET BEGIN
      MOV [28H], AX
      MOV AX, SEG  BEGIN
      MOV [2AH], AX
      ;************************************************
      STI;   ；开放CPU 对可屏蔽中断的响应
      HLT       
  code1 ends
      
 2 0 2 0 - 0 8 - 1 0
{,   8 0ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 82 页 ===

code2 segment  
INTR   PROC   FAR
BEGIN: PUSH AX
        STI         ;允许中断嵌套
       
        IN AL,  0D0H; 读取A口状态
        OR AL,0F0H;  屏蔽高四位，置位
        OUT 0D2H, AL;发送给B 口
       
        CLI        ;关中断
       
        MOV AL,20H      ; 0010 0000B
        OUT 0C0H, AL   ;给8259A发送一般中断结束命令
       
        POP AX;
        IRET 
    
INTR ENDP
code2 ends
      
 2 0 2 0 - 0 8 - 1 0
{,   8 1ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 83 页 ===

51单片机是8 位机，地址总线16位，数据总线8 位 •
CPU：运算器+ 控制器 •
存放机器代码、常数表格○
寻址范围：0000H~FFFFH    64K ○
寻址指针：PC，DPTR○
0000H 单元：上电复位时，程序计数器PC所指向的单元 
0003H 单元： /INT0 外部中断0 的入口地址； 
000BH 单元：定时器T0溢出中断的入口地址； 
0013H 单元： /INT1 外部中断1 的入口地址； 
001BH 单元：定时器T1溢出中断的入口地址； 
0023H 单元：串行口接收、发送中断的入口地址； 6个特殊存储单元【强调】○内部程序存储器ROM【掌握】•
功能分区【掌握】RAM：工作寄存器区，位寻址区，栈区，数据存储区（少量）○
以上各区的地址熟练掌握
内部数据存储器RAM：128B的 RAM  +128B 的 SFR（特殊功能寄存器） •一、80C51 的基本结构和功能单片机部分 期末总结
2020年8 月10日1:54
      
 2 0 2 0 - 0 8 - 1 0
{,   8 2ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 84 页 ===

上电或复位后，SP被初始化为07H ，但是这个位置是工作寄存器区，所
以要重新初始化，如MOV  SP,  #60H。□
C51的堆栈和数据结构中学的堆栈相同，入栈栈顶SP增加，8086 的是
减少。□堆栈指针SP【毕业生考试考了】
用来设定单片机内部各个功能模块的工作方式：
中断方式的设定、定时器工作模式的设定;
存放各功能模块的状态、标志等
标志位【强调】
在加法运算中, 累加器A 的最高位A7有进位, 则CY=1, 否则CY=0.
在减法运算中, 如果A7有借位, 则CY=1.CY (PSW.7) 进位标志:   
         CY往往作为无符号数运算是否有溢出的标志。
用来判断加减法运算时, 低四位是否向高四位进位或借位( 既A3的进位
或借位).
用来判断压缩的BCD码的运算处理.AC(PSW.6): 辅助进位位:   
完全由用户来定义和使用。F0(PSW.5)  用户标志位:    
确定工作寄存器R0‐ R7在哪个区中.
单片机在上电或复位后RS1、RS0=00 。
可通过指令修改RS1,RS0 的值来改变工作寄存器区的位 置。RS1,RS0(PSW4,3) 工作寄存器区选择位:SFR：○
四个端口都是具有输出锁存功能的
准双向端口。 ○
P0口作为数据I/O 端口，必须外接上拉电阻。 ○
P0和P2既能作为通用I/O 口，也能作为地址总线端口，其中P0用于低8 位，P2用于
高8位。○
P3通常不用作通用I/O 口，用作控制总线（第二功能）。 ○
在进行引脚输入操作前，需先向口输出锁存器置1 ，使下方的FET 截止。 ○
单片机复位后，绝大部分寄存器都是0 ，但P0~P3口全部置1 。 ○4个8位并行输入/ 输出端口：P0、P1、P2和P3【加粗字重点】 •
中断系统：设有5 个中断源； •
震荡周期T ：时序中最小的时间单位。 ○
时钟周期2T：晶体震荡器的震荡信号经过片内时钟发生器二分频后的信号。时钟○三个周期概念【了解】•
      
 2 0 2 0 - 0 8 - 1 0
{,   8 3ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 85 页 ===

周期是震荡周期的2 倍。
机器周期：CPU执行一条指令所需要的时间的基本单位。51单片机中的机器周期由
12个震荡周期构成，分为6 个状态周期○
○
主要用来延时模式1 ——16位计数器模式 ○
主要用于给串口提供发送或接收时钟模式2 ——8 位自动重装初值模式（循环计数） ○
溢出时，溢出标志TF0会被置1 ，引发T0溢出中断，或用于查询。 中断+ 查询 ○定时器【模式1 和模式2是重点】 •
串行口控制寄存器SCON，地址98H○
SM0  SM1:  串行口工作模式选择位。 ○
初始化时软件清零，RI    一帧数据接收完成的标志 ○
      一帧接收完成后，硬件自动置位RI=1，     
     如果中断开放，此置1 标志位作为中断申请，引发串口中断；
初始化时软件清零，TI    一帧数据发送完成的标志 ○
如果中断开放，此置1 标志位作为中断申请，引发串口中断；      一帧发送完成后，硬件自动置位TI=1，串口【4种工作模式要记住，特别注意模式0 ，还有RI、TI的功能】 •最大计数长度2位数个机器周期
      
 2 0 2 0 - 0 8 - 1 0
{,   8 4ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 86 页 ===

软件延时○
自动去抖动硬件电路○消除按键抖动•
独立按键•
第一种：看PPT，需要把行或列中的一个接+5V，另一个接地。□
第二种：用输出电平的方式取代外接+5V和地。□两种办法扫描法判断按了哪个键○矩阵按键•二、键盘/ 按键串行口扩展并行I/O口，可以扩展n*8 位的并行I/O口。
RXD(P3.0)做 数据线（双向）
TXD(P3.1)做 移位脉冲输出端
串入并出，每右移1 位需要一个时钟周期 
并入串出，每左移1 位需要一个时钟周期 
模式0：同步移位寄存器 ○
      
 2 0 2 0 - 0 8 - 1 0
{,   8 5ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 87 页 ===

第二种的代码（自己写的，用语言描述不如用代码更清楚）
与上图键盘接线相对应，在主函数中不断循环该函数进行扫描判断。
     1 #define  GPIO_KEY  P1
     2 void Key_Matrix()
     3 {
     4      unsigned  int wait_time  = 0;
     5      GPIO_KEY  = 0x0F;     //设定初始高四位低电平低四位高电平
     6      if(GPIO_KEY  != 0x0F) //当有按键按下，低四位的某个0 会变成 1
     7      {
     8          delay(1000);      //消抖
     9          if(GPIO_KEY  != 0x0F)  //再确认有按键按下
  10          {
  11              switch (GPIO_KEY)   //判断低四位哪位为0 ，得到被按下的键在哪列
  12              {
  13            
      case (0x07):  key_row  = 0; break ;
  14                  case (0x0B):  key_row  = 1; break ;
  15                  case (0x0D):  key_row  = 2; break ;
  16                  case (0x0E):  key_row  = 3; break ;
  17              }
  18              GPIO_KEY  = 0xF0;     //改设定高四位高电平低四位低电平
  19              switch (GPIO_KEY)   //判断低四位哪位为0 ，得到被按下的键在哪行
  20              {
  21                  case (0x70):  key_line = 0; brea k;
  22   
               case (0xB0):  key_line  = 1; break ;
  23                  case (0xD0):  key_line  = 2; break ;
  24                  case (0xE0):  key_line  = 3; break ;
  25              }
             //按下一个键1 秒钟，强制退出判断  26              while ((wait_time  < 100) && (GPIO_KEY  != 0xF0))
  27              {
  28                  delay(1000);
  29                  wait_time++;
  30              }
  31         }
  32   
  }
  33 }
  34 
分清楚阳极接法还是阴极接法。○
接上大电阻防止击穿。○单个灯泡（通常并行8 个） •
也分为共阴极共阳极的两种接法。○
共阴极
74245 不要也可以，直接接a ‐dp直接接I/O数码管•三、LED
      
 2 0 2 0 - 0 8 - 1 0
{,   8 6ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3

=== 第 88 页 ===

码表○
静态显示，直接与8 位I/O口相接，输出电平 ○
用74138 译码器，对8 个（几个都行）数码管进行循环片选。 
通过I/O口对每次被片选中的数码管输出对应电平。
循环片选速度足够快，人看不到频闪，伪装成并行输出。
缺点：亮度不够、片选的循环体中不能写太多语句（影响速度）。动态显示○
      
 2 0 2 0 - 0 8 - 1 0
{,   8 7ÿ 8 7u
T^sg\O^UY'\û  
]òPR6bSSpRýÿY bSSp~¸(÷yÁb3