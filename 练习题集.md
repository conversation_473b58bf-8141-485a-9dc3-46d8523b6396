# 微机原理及单片机应用 - 针对性练习题集

## 📚 练习题目概览

### 🎯 题目设计原则
- **针对性强**：基于知识框架分析的高频考点
- **难度分层**：基础题、提高题、综合题三个层次
- **题型多样**：选择题、填空题、计算题、编程题、简答题
- **实用性高**：模拟真实考试环境和题型

### 📊 题目分布统计
| 知识点 | 基础题 | 提高题 | 综合题 | 总计 |
|--------|--------|--------|--------|------|
| 中断向量表计算 | 6题 | 3题 | 1题 | 10题 |
| 物理地址计算 | 8题 | 5题 | 2题 | 15题 |
| 8259A中断控制器 | 4题 | 3题 | 1题 | 8题 |
| 8051定时器编程 | 5题 | 4题 | 2题 | 11题 |
| 8255A接口编程 | 4题 | 3题 | 1题 | 8题 |
| 标志寄存器应用 | 6题 | 3题 | 1题 | 10题 |
| 串行通信编程 | 3题 | 2题 | 1题 | 6题 |
| 综合应用 | 2题 | 3题 | 3题 | 8题 |
| **总计** | **38题** | **26题** | **12题** | **76题** |

---

## 🔥 第一部分：中断向量表计算（10题）

### 基础题（6题）

#### 题目1：中断向量地址计算
**题目**：中断类型号为15H的中断向量在中断向量表中的存放地址是多少？

**解答**：
- 中断向量地址 = 中断类型号 × 4
- = 15H × 4 = 54H
- 存放地址：0000:0054H - 0000:0057H

**知识点**：中断向量表基本计算
**难度**：★★☆☆☆
**答题时间**：1分钟

#### 题目2：中断向量填表
**题目**：若中断类型号为20H的中断服务程序入口地址为2000:1500H，请在中断向量表中填写相应内容。

**解答**：
- 中断向量地址 = 20H × 4 = 80H
- 填写内容：
  - 0000:0080H = 00H (IP低字节)
  - 0000:0081H = 15H (IP高字节)
  - 0000:0082H = 00H (CS低字节)
  - 0000:0083H = 20H (CS高字节)

**知识点**：中断向量表填写
**难度**：★★★☆☆
**答题时间**：2分钟

#### 题目3：选择题
**题目**：8086系统中，中断向量表占用的内存空间是（）
A. 256字节  B. 512字节  C. 1024字节  D. 2048字节

**解答**：C
- 中断向量表有256个中断向量
- 每个中断向量占4字节
- 总空间 = 256 × 4 = 1024字节

**知识点**：中断向量表结构
**难度**：★★☆☆☆
**答题时间**：30秒

#### 题目4：填空题
**题目**：中断向量表位于内存的_____地址到_____地址，共有_____个中断向量，每个中断向量占_____字节。

**解答**：00000H，003FFH，256，4

**知识点**：中断向量表基本概念
**难度**：★★☆☆☆
**答题时间**：1分钟

#### 题目5：地址查找
**题目**：已知中断向量表中地址0000:006CH开始的4个字节内容为：34H、12H、78H、56H，求对应的中断服务程序入口地址。

**解答**：
- 中断类型号 = 6CH ÷ 4 = 1BH
- IP = 1234H (低字节在前)
- CS = 5678H (低字节在前)
- 入口地址 = 5678:1234H

**知识点**：中断向量表查找
**难度**：★★★☆☆
**答题时间**：2分钟

#### 题目6：计算题
**题目**：某系统有8个中断源，中断类型号分别为08H-0FH，计算这些中断向量在中断向量表中的位置。

**解答**：
- 08H: 08H × 4 = 20H (0000:0020H-0000:0023H)
- 09H: 09H × 4 = 24H (0000:0024H-0000:0027H)
- 0AH: 0AH × 4 = 28H (0000:0028H-0000:002BH)
- 0BH: 0BH × 4 = 2CH (0000:002CH-0000:002FH)
- 0CH: 0CH × 4 = 30H (0000:0030H-0000:0033H)
- 0DH: 0DH × 4 = 34H (0000:0034H-0000:0037H)
- 0EH: 0EH × 4 = 38H (0000:0038H-0000:003BH)
- 0FH: 0FH × 4 = 3CH (0000:003CH-0000:003FH)

**知识点**：批量中断向量计算
**难度**：★★★☆☆
**答题时间**：3分钟

### 提高题（3题）

#### 题目7：综合分析
**题目**：某微机系统中，中断向量表的内容如下：
- 0000:0020H: 00H, 30H, 00H, 10H
- 0000:0024H: 50H, 25H, 00H, 20H
求：(1)对应的中断类型号；(2)中断服务程序的入口地址

**解答**：
(1) 中断类型号：
- 第一个：20H ÷ 4 = 08H
- 第二个：24H ÷ 4 = 09H

(2) 入口地址：
- 08H中断：1000:3000H
- 09H中断：2000:2550H

**知识点**：中断向量表综合分析
**难度**：★★★★☆
**答题时间**：4分钟

#### 题目8：错误分析
**题目**：某学生在设置中断类型号为18H的中断向量时，将入口地址3000:2000H错误地填写为：
0000:0060H: 00H, 20H, 00H, 30H
请指出错误并给出正确答案。

**解答**：
错误分析：
1. 地址计算错误：18H × 4 = 60H ✓
2. IP填写错误：应为00H, 20H ✓
3. CS填写错误：应为00H, 30H ✓

实际上填写是正确的，题目可能有误。正确填写应该是：
- 0000:0060H: 00H, 20H, 00H, 30H

**知识点**：中断向量表错误分析
**难度**：★★★★☆
**答题时间**：3分钟

#### 题目9：系统设计
**题目**：设计一个中断系统，要求：
(1) 有4个中断源，优先级从高到低
(2) 中断类型号为60H-63H
(3) 中断服务程序分别位于4000:1000H, 4000:2000H, 4000:3000H, 4000:4000H
请给出中断向量表的设置。

**解答**：
中断向量表设置：
- 60H中断 (地址180H): 00H, 10H, 00H, 40H
- 61H中断 (地址184H): 00H, 20H, 00H, 40H
- 62H中断 (地址188H): 00H, 30H, 00H, 40H
- 63H中断 (地址18CH): 00H, 40H, 00H, 40H

**知识点**：中断系统设计
**难度**：★★★★☆
**答题时间**：5分钟

### 综合题（1题）

#### 题目10：中断系统综合应用
**题目**：某工控系统使用8259A管理中断，设置如下：
- ICW1 = 13H (边沿触发，单片，需要ICW4)
- ICW2 = 20H (中断类型20H-27H)
- ICW4 = 01H (普通嵌套，非缓冲，手动EOI)
- 中断服务程序统一位于段地址3000H

要求：
(1) 分析8259A的配置
(2) 设计中断向量表
(3) 编写IR0的中断服务程序框架

**解答**：
(1) 8259A配置分析：
- 边沿触发方式，适合脉冲中断信号
- 单片模式，系统中只有一个8259A
- 中断类型号20H-27H，对应IR0-IR7
- 普通嵌套，高优先级可中断低优先级

(2) 中断向量表设计：
```
20H中断(80H): 00H, 00H, 00H, 30H  ; IR0
21H中断(84H): 00H, 10H, 00H, 30H  ; IR1
22H中断(88H): 00H, 20H, 00H, 30H  ; IR2
...
27H中断(9CH): 00H, 70H, 00H, 30H  ; IR7
```

(3) IR0中断服务程序：
```assembly
INT20H PROC FAR
    STI                    ; 开中断
    PUSH AX               ; 保存寄存器
    PUSH BX
    ; 中断处理代码
    MOV AL, 20H           ; 发送EOI命令
    OUT 20H, AL
    POP BX                ; 恢复寄存器
    POP AX
    CLI                   ; 关中断
    IRET                  ; 中断返回
INT20H ENDP
```

**知识点**：中断系统综合设计
**难度**：★★★★★
**答题时间**：10分钟

---

## 🔥 第二部分：物理地址计算（15题）

### 基础题（8题）

#### 题目11：基本地址计算
**题目**：已知段地址为1234H，偏移地址为5678H，求物理地址。

**解答**：
- 物理地址 = 段地址 × 16 + 偏移地址
- = 1234H × 16 + 5678H
- = 12340H + 5678H = 179B8H

**知识点**：物理地址基本计算
**难度**：★★☆☆☆
**答题时间**：1分钟

#### 题目12：逆向计算
**题目**：已知物理地址为50000H，段地址为4000H，求偏移地址。

**解答**：
- 偏移地址 = 物理地址 - 段地址 × 16
- = 50000H - 4000H × 16
- = 50000H - 40000H = 10000H

**知识点**：偏移地址计算
**难度**：★★★☆☆
**答题时间**：1.5分钟

#### 题目13：选择题
**题目**：8086的地址总线有20位，能直接寻址的内存空间是（）
A. 64KB  B. 1MB  C. 16MB  D. 4GB

**解答**：B
- 20位地址总线可寻址 2^20 = 1MB 空间

**知识点**：8086寻址能力
**难度**：★★☆☆☆
**答题时间**：30秒

#### 题目14：填空题
**题目**：8086采用_____位地址总线，_____位数据总线，最大寻址空间为_____。

**解答**：20，16，1MB

**知识点**：8086基本特性
**难度**：★★☆☆☆
**答题时间**：30秒

#### 题目15：段地址计算
**题目**：某数据存放在物理地址3F800H处，若段地址为3F00H，求偏移地址；若偏移地址为0800H，求段地址。

**解答**：
情况1：段地址3F00H
- 偏移地址 = 3F800H - 3F00H × 16
- = 3F800H - 3F000H = 800H

情况2：偏移地址0800H
- 段地址 × 16 = 3F800H - 0800H = 3F000H
- 段地址 = 3F000H ÷ 16 = 3F00H

**知识点**：段地址与偏移地址关系
**难度**：★★★☆☆
**答题时间**：2分钟

#### 题目16：地址范围
**题目**：段地址为2000H的段，其地址范围是多少？

**解答**：
- 段起始地址 = 2000H × 16 = 20000H
- 段结束地址 = 20000H + FFFFH = 2FFFFH
- 地址范围：20000H - 2FFFFH

**知识点**：段地址范围计算
**难度**：★★★☆☆
**答题时间**：1.5分钟

#### 题目17：重叠段
**题目**：段地址1000H和段地址1001H的段是否重叠？如果重叠，重叠部分有多大？

**解答**：
- 段1000H范围：10000H - 1FFFFH
- 段1001H范围：10010H - 2000FH
- 重叠部分：10010H - 1FFFFH
- 重叠大小 = 1FFFFH - 10010H + 1 = FFF0H = 65520字节

**知识点**：段重叠分析
**难度**：★★★★☆
**答题时间**：3分钟

#### 题目18：地址转换
**题目**：将以下逻辑地址转换为物理地址：
(1) 2000:3000H  (2) FFFF:0010H  (3) 0000:FFFFH

**解答**：
(1) 2000:3000H = 2000H × 16 + 3000H = 20000H + 3000H = 23000H
(2) FFFF:0010H = FFFFH × 16 + 0010H = FFFF0H + 10H = 100000H
(3) 0000:FFFFH = 0000H × 16 + FFFFH = 0H + FFFFH = FFFFFH

**知识点**：逻辑地址到物理地址转换
**难度**：★★★☆☆
**答题时间**：2分钟

### 提高题（5题）

#### 题目19：地址计算综合
**题目**：某程序中有以下指令序列：
```
MOV AX, 1234H
MOV DS, AX
MOV SI, 5678H
MOV AL, [SI]
```
求MOV AL, [SI]指令访问的物理地址。

**解答**：
- DS = 1234H
- SI = 5678H
- 物理地址 = DS × 16 + SI = 1234H × 16 + 5678H
- = 12340H + 5678H = 179B8H

**知识点**：指令地址计算
**难度**：★★★★☆
**答题时间**：2分钟

#### 题目20：存储器分配
**题目**：某系统内存1MB，按段分配：
- 系统段：0000H-0FFFH
- 用户代码段：1000H-3FFFH
- 用户数据段：4000H-7FFFH
- 缓冲区：8000H-BFFFH
计算各段的物理地址范围和大小。

**解答**：
- 系统段：00000H-0FFFFH，大小64KB
- 用户代码段：10000H-3FFFFH，大小192KB
- 用户数据段：40000H-7FFFFH，大小256KB
- 缓冲区：80000H-BFFFFH，大小256KB
- 剩余空间：C0000H-FFFFFH，大小256KB

**知识点**：存储器分配管理
**难度**：★★★★☆
**答题时间**：4分钟

#### 题目21：地址映射
**题目**：某外设接口卡占用I/O地址300H-30FH，若通过内存映射方式访问，映射到物理地址A0000H开始的区域，写出访问各I/O端口的内存地址。

**解答**：
I/O地址到内存地址映射：
- 300H → A0000H
- 301H → A0001H
- 302H → A0002H
- ...
- 30FH → A000FH

映射公式：内存地址 = A0000H + (I/O地址 - 300H)

**知识点**：I/O地址映射
**难度**：★★★★☆
**答题时间**：3分钟

#### 题目22：地址计算错误分析
**题目**：某学生计算物理地址时出现以下错误：
段地址2000H，偏移地址8000H
错误计算：2000H + 8000H = A000H
请指出错误并给出正确答案。

**解答**：
错误分析：
- 错误：直接相加段地址和偏移地址
- 正确公式：物理地址 = 段地址 × 16 + 偏移地址

正确计算：
- 物理地址 = 2000H × 16 + 8000H
- = 20000H + 8000H = 28000H

**知识点**：地址计算常见错误
**难度**：★★★★☆
**答题时间**：2分钟

#### 题目23：复杂地址计算
**题目**：某程序运行时的寄存器状态：
CS=2000H, IP=1000H, DS=3000H, SS=4000H, SP=2000H
计算：
(1) 当前指令地址
(2) 数据段起始地址
(3) 栈顶地址

**解答**：
(1) 当前指令地址 = CS × 16 + IP = 2000H × 16 + 1000H = 21000H
(2) 数据段起始地址 = DS × 16 = 3000H × 16 = 30000H
(3) 栈顶地址 = SS × 16 + SP = 4000H × 16 + 2000H = 42000H

**知识点**：寄存器地址计算
**难度**：★★★★☆
**答题时间**：3分钟

### 综合题（2题）

#### 题目24：存储器系统设计
**题目**：设计一个8086系统的存储器分配方案：
- 总内存：512KB
- 系统ROM：64KB，位于高地址
- 用户RAM：448KB，位于低地址
- 要求给出段地址分配和物理地址范围

**解答**：
存储器分配方案：
- 总地址空间：00000H-7FFFFH (512KB)
- 用户RAM：00000H-6FFFFH (448KB)
  - 段地址范围：0000H-6FFFH
- 系统ROM：70000H-7FFFFH (64KB)
  - 段地址范围：7000H-7FFFH

段地址分配建议：
- 中断向量表：0000H段
- 用户代码段：1000H-3FFFH段
- 用户数据段：4000H-6FFFH段
- 系统ROM：7000H-7FFFH段

**知识点**：存储器系统设计
**难度**：★★★★★
**答题时间**：8分钟

#### 题目25：地址计算综合应用
**题目**：某程序需要访问一个数组，数组基地址为DS:1000H，每个元素2字节，要访问第10个元素（从0开始计数）。
(1) 计算该元素的偏移地址
(2) 若DS=2000H，计算物理地址
(3) 写出访问该元素的汇编指令

**解答**：
(1) 元素偏移地址计算：
- 基地址偏移：1000H
- 元素偏移：10 × 2 = 20 = 14H
- 总偏移地址：1000H + 14H = 1014H

(2) 物理地址计算：
- 物理地址 = DS × 16 + 偏移地址
- = 2000H × 16 + 1014H = 20000H + 1014H = 21014H

(3) 汇编指令：
```assembly
MOV SI, 1000H      ; 基地址
ADD SI, 14H        ; 加上元素偏移
MOV AX, [SI]       ; 访问元素
; 或者直接：
MOV AX, [1014H]    ; 直接访问
```

**知识点**：数组地址计算
**难度**：★★★★★
**答题时间**：6分钟

---

## 🔥 第三部分：8259A中断控制器（8题）

### 基础题（4题）

#### 题目26：8259A初始化
**题目**：对8259A进行初始化，要求：边沿触发，单片工作，中断类型08H-0FH，普通嵌套，非缓冲，手动EOI。设端口地址为20H和21H。

**解答**：
```assembly
MOV AL, 13H        ; ICW1: 边沿触发，单片，需要ICW4
OUT 20H, AL
MOV AL, 08H        ; ICW2: 中断类型08H-0FH
OUT 21H, AL
MOV AL, 01H        ; ICW4: 普通嵌套，非缓冲，手动EOI
OUT 21H, AL
```

**知识点**：8259A基本初始化
**难度**：★★★☆☆
**答题时间**：3分钟

#### 题目27：中断屏蔽
**题目**：要屏蔽8259A的IR1、IR3、IR5中断，其他中断正常，写出设置IMR的指令。

**解答**：
```assembly
MOV AL, 00101010B  ; 屏蔽IR1、IR3、IR5
OUT 21H, AL        ; 写入IMR寄存器
```
或者：
```assembly
MOV AL, 2AH        ; 2AH = 00101010B
OUT 21H, AL
```

**知识点**：中断屏蔽寄存器IMR
**难度**：★★★☆☆
**答题时间**：2分钟

#### 题目28：EOI命令
**题目**：在IR2的中断服务程序结束时，应该发送什么EOI命令？写出相应指令。

**解答**：
```assembly
; 方法1：非特殊EOI
MOV AL, 20H
OUT 20H, AL

; 方法2：特殊EOI
MOV AL, 62H        ; 60H + IR2号(2)
OUT 20H, AL
```

**知识点**：EOI命令
**难度**：★★★☆☆
**答题时间**：2分钟

#### 题目29：选择题
**题目**：8259A可以管理（）个中断源
A. 4个  B. 6个  C. 8个  D. 16个

**解答**：C
8259A有8个中断输入引脚IR0-IR7，可管理8个中断源。

**知识点**：8259A基本特性
**难度**：★★☆☆☆
**答题时间**：30秒

### 提高题（3题）

#### 题目30：级联8259A
**题目**：某系统使用两片8259A级联，主片端口地址20H/21H，从片端口地址A0H/A1H，从片连接到主片的IR2。主片中断类型08H-0FH，从片中断类型70H-77H。写出初始化程序。

**解答**：
```assembly
; 主片初始化
MOV AL, 11H        ; ICW1: 边沿触发，级联，需要ICW4
OUT 20H, AL
MOV AL, 08H        ; ICW2: 中断类型08H-0FH
OUT 21H, AL
MOV AL, 04H        ; ICW3: IR2连接从片
OUT 21H, AL
MOV AL, 01H        ; ICW4: 普通嵌套，非缓冲，手动EOI
OUT 21H, AL

; 从片初始化
MOV AL, 11H        ; ICW1: 边沿触发，级联，需要ICW4
OUT A0H, AL
MOV AL, 70H        ; ICW2: 中断类型70H-77H
OUT A1H, AL
MOV AL, 02H        ; ICW3: 连接到主片IR2
OUT A1H, AL
MOV AL, 01H        ; ICW4: 普通嵌套，非缓冲，手动EOI
OUT A1H, AL
```

**知识点**：8259A级联应用
**难度**：★★★★☆
**答题时间**：5分钟

#### 题目31：中断优先级
**题目**：8259A的默认优先级是IR0最高，IR7最低。现要求改变优先级为IR3最高，其他按循环顺序。写出设置程序。

**解答**：
```assembly
; 设置旋转优先级，IR3最高
MOV AL, 0C3H       ; 旋转优先级命令 + IR3
OUT 20H, AL
```
设置后优先级顺序：IR3 > IR4 > IR5 > IR6 > IR7 > IR0 > IR1 > IR2

**知识点**：8259A优先级控制
**难度**：★★★★☆
**答题时间**：3分钟

#### 题目32：状态读取
**题目**：要读取8259A的IRR（中断请求寄存器）和ISR（中断服务寄存器）的状态，写出相应程序。

**解答**：
```assembly
; 读取IRR
MOV AL, 0AH        ; OCW3: 读取IRR
OUT 20H, AL
IN AL, 20H         ; 读取IRR状态

; 读取ISR
MOV AL, 0BH        ; OCW3: 读取ISR
OUT 20H, AL
IN AL, 20H         ; 读取ISR状态
```

**知识点**：8259A状态读取
**难度**：★★★★☆
**答题时间**：3分钟

### 综合题（1题）

#### 题目33：8259A系统设计
**题目**：设计一个基于8259A的中断系统：
- 8个中断源：键盘、鼠标、串口1、串口2、并口、定时器、网卡、声卡
- 优先级：定时器 > 键盘 > 鼠标 > 串口1 > 串口2 > 并口 > 网卡 > 声卡
- 中断类型号：20H-27H
- 端口地址：20H/21H

要求：(1)分配中断号 (2)写初始化程序 (3)设计中断服务程序框架

**解答**：
(1) 中断号分配（按优先级）：
- IR0: 定时器 (20H)
- IR1: 键盘 (21H)
- IR2: 鼠标 (22H)
- IR3: 串口1 (23H)
- IR4: 串口2 (24H)
- IR5: 并口 (25H)
- IR6: 网卡 (26H)
- IR7: 声卡 (27H)

(2) 初始化程序：
```assembly
; 8259A初始化
MOV AL, 13H        ; ICW1
OUT 20H, AL
MOV AL, 20H        ; ICW2: 中断类型20H-27H
OUT 21H, AL
MOV AL, 01H        ; ICW4
OUT 21H, AL
MOV AL, 00H        ; 开放所有中断
OUT 21H, AL
```

(3) 中断服务程序框架：
```assembly
TIMER_INT PROC FAR     ; 定时器中断
    STI
    PUSH AX
    ; 定时器处理代码
    MOV AL, 20H
    OUT 20H, AL        ; 发送EOI
    POP AX
    CLI
    IRET
TIMER_INT ENDP

KEYBOARD_INT PROC FAR  ; 键盘中断
    STI
    PUSH AX
    ; 键盘处理代码
    MOV AL, 20H
    OUT 20H, AL
    POP AX
    CLI
    IRET
KEYBOARD_INT ENDP
```

**知识点**：8259A系统综合设计
**难度**：★★★★★
**答题时间**：10分钟

---

## 🔥 第四部分：8051定时器编程（11题）

### 基础题（5题）

#### 题目34：定时器初值计算
**题目**：使用8051定时器T0产生10ms定时，晶振频率12MHz，工作在模式1。求初值。

**解答**：
- 机器周期 = 12/12MHz = 1μs
- 定时10ms需要：10ms/1μs = 10000个机器周期
- 初值 = 65536 - 10000 = 55536 = D8F0H
- TH0 = D8H, TL0 = F0H

**知识点**：定时器初值计算
**难度**：★★★☆☆
**答题时间**：2分钟

#### 题目35：定时器模式设置
**题目**：设置定时器T0工作在模式2，T1工作在模式1，写出TMOD的设置指令。

**解答**：
```c
TMOD = 0x12;       // T1模式1，T0模式2
```
或汇编：
```assembly
MOV TMOD, #12H
```
TMOD = 00010010B

**知识点**：TMOD寄存器设置
**难度**：★★★☆☆
**答题时间**：1分钟

#### 题目36：定时器启动
**题目**：启动定时器T0和T1，并允许它们的中断，写出相应的C语言程序。

**解答**：
```c
TR0 = 1;           // 启动定时器T0
TR1 = 1;           // 启动定时器T1
ET0 = 1;           // 允许T0中断
ET1 = 1;           // 允许T1中断
EA = 1;            // 开总中断
```

**知识点**：定时器控制
**难度**：★★☆☆☆
**答题时间**：1分钟

#### 题目37：选择题
**题目**：8051定时器工作在模式2时的特点是（）
A. 13位计数  B. 16位计数  C. 8位自动重装  D. 双8位计数

**解答**：C
模式2是8位自动重装模式，计数器溢出后自动重装初值。

**知识点**：定时器工作模式
**难度**：★★☆☆☆
**答题时间**：30秒

#### 题目38：填空题
**题目**：8051有_____个定时器，分别是_____和_____，每个定时器有_____种工作模式。

**解答**：2，T0，T1，4

**知识点**：8051定时器基本概念
**难度**：★★☆☆☆
**答题时间**：30秒

### 提高题（4题）

#### 题目39：PWM波形生成
**题目**：使用定时器T0生成频率1KHz，占空比30%的PWM波形，晶振12MHz。写出完整程序。

**解答**：
```c
#include <reg52.h>

sbit PWM_PIN = P1^0;
unsigned char counter = 0;

void Timer0_Init() {
    TMOD |= 0x01;      // T0模式1
    TH0 = 0xFC;        // 1ms定时
    TL0 = 0x18;
    TR0 = 1;
    ET0 = 1;
    EA = 1;
}

void Timer0_ISR() interrupt 1 {
    TH0 = 0xFC;
    TL0 = 0x18;
    counter++;
    if(counter <= 30) {    // 30%占空比
        PWM_PIN = 1;
    } else {
        PWM_PIN = 0;
    }
    if(counter >= 100) {   // 1KHz周期
        counter = 0;
    }
}

void main() {
    Timer0_Init();
    while(1);
}
```

**知识点**：PWM波形生成
**难度**：★★★★☆
**答题时间**：8分钟

#### 题目40：定时器级联
**题目**：使用T0和T1级联产生1秒定时，晶振12MHz。T0工作在模式1，T1工作在计数模式。

**解答**：
```c
#include <reg52.h>

void Timer_Init() {
    TMOD = 0x51;       // T1计数模式，T0定时模式1

    // T0产生1ms脉冲
    TH0 = 0xFC;
    TL0 = 0x18;

    // T1计数1000次
    TH1 = 0xFC;        // 1000 = 1024-24 = 400H-18H
    TL1 = 0x18;

    TR0 = 1;
    TR1 = 1;
    ET1 = 1;           // T1中断
    EA = 1;
}

void Timer1_ISR() interrupt 3 {
    TH1 = 0xFC;        // 重装初值
    TL1 = 0x18;
    // 1秒定时到达
}
```

**知识点**：定时器级联应用
**难度**：★★★★☆
**答题时间**：6分钟

#### 题目41：频率测量
**题目**：使用定时器测量外部信号频率，测量时间1秒，写出测量程序框架。

**解答**：
```c
#include <reg52.h>

unsigned int pulse_count = 0;
unsigned char time_flag = 0;

void Timer0_Init() {
    // T0定时1秒
    TMOD |= 0x01;
    TH0 = 0x3C;        // 50ms定时
    TL0 = 0xB0;
    TR0 = 1;
    ET0 = 1;
    EA = 1;
}

void Timer1_Init() {
    // T1计数模式
    TMOD |= 0x50;
    TH1 = 0;
    TL1 = 0;
    TR1 = 1;
}

void Timer0_ISR() interrupt 1 {
    static unsigned char count_50ms = 0;
    TH0 = 0x3C;
    TL0 = 0xB0;
    count_50ms++;
    if(count_50ms >= 20) {  // 1秒到
        count_50ms = 0;
        time_flag = 1;
        TR1 = 0;            // 停止计数
        pulse_count = TH1 * 256 + TL1;
        TH1 = 0;            // 清零重新开始
        TL1 = 0;
        TR1 = 1;
    }
}

void main() {
    Timer0_Init();
    Timer1_Init();
    while(1) {
        if(time_flag) {
            time_flag = 0;
            // pulse_count即为频率值
        }
    }
}
```

**知识点**：频率测量应用
**难度**：★★★★★
**答题时间**：10分钟

#### 题目42：定时器中断嵌套
**题目**：T0产生1ms中断（高优先级），T1产生10ms中断（低优先级）。在T1中断中需要执行较长时间的任务，如何设计程序避免T0中断丢失？

**解答**：
```c
#include <reg52.h>

void Timer_Init() {
    TMOD = 0x11;       // 两个定时器都工作在模式1

    // T0: 1ms定时
    TH0 = 0xFC;
    TL0 = 0x18;

    // T1: 10ms定时
    TH1 = 0xD8;
    TL1 = 0xF0;

    PT0 = 1;           // T0高优先级
    PT1 = 0;           // T1低优先级

    TR0 = 1;
    TR1 = 1;
    ET0 = 1;
    ET1 = 1;
    EA = 1;
}

void Timer0_ISR() interrupt 1 {
    TH0 = 0xFC;
    TL0 = 0x18;
    // 高优先级，快速处理
}

void Timer1_ISR() interrupt 3 {
    TH1 = 0xD8;
    TL1 = 0xF0;
    EA = 1;            // 重新开中断，允许T0中断
    // 长时间任务处理
    // T0可以中断此处理过程
}
```

**知识点**：中断优先级和嵌套
**难度**：★★★★★
**答题时间**：8分钟

### 综合题（2题）

#### 题目43：数字时钟设计
**题目**：设计一个数字时钟，要求：
- 显示时:分:秒格式
- 使用6个数码管显示
- 定时器产生1秒时基
- 晶振12MHz

写出核心程序框架。

**解答**：
```c
#include <reg52.h>

unsigned char hour = 12, minute = 0, second = 0;
unsigned char display_data[6];
unsigned char code digit_table[] = {0x3F,0x06,0x5B,0x4F,0x66,0x6D,0x7D,0x07,0x7F,0x6F};

void Timer0_Init() {
    TMOD |= 0x01;      // 模式1
    TH0 = 0x3C;        // 50ms定时
    TL0 = 0xB0;
    TR0 = 1;
    ET0 = 1;
    EA = 1;
}

void Update_Display() {
    display_data[0] = hour / 10;
    display_data[1] = hour % 10;
    display_data[2] = minute / 10;
    display_data[3] = minute % 10;
    display_data[4] = second / 10;
    display_data[5] = second % 10;
}

void Timer0_ISR() interrupt 1 {
    static unsigned char count_50ms = 0;
    TH0 = 0x3C;
    TL0 = 0xB0;
    count_50ms++;
    if(count_50ms >= 20) {  // 1秒到
        count_50ms = 0;
        second++;
        if(second >= 60) {
            second = 0;
            minute++;
            if(minute >= 60) {
                minute = 0;
                hour++;
                if(hour >= 24) {
                    hour = 0;
                }
            }
        }
        Update_Display();
    }
}

void Display_Scan() {
    static unsigned char scan_pos = 0;
    P0 = 0xFF;                    // 消隐
    P2 = ~(1 << scan_pos);        // 位选
    P0 = digit_table[display_data[scan_pos]];  // 段选
    scan_pos++;
    if(scan_pos >= 6) scan_pos = 0;
}

void main() {
    Timer0_Init();
    Update_Display();
    while(1) {
        Display_Scan();
        // 延时1ms
    }
}
```

**知识点**：数字时钟综合设计
**难度**：★★★★★
**答题时间**：15分钟

#### 题目44：多功能定时器应用
**题目**：设计一个多功能定时器系统：
- 功能1：产生1KHz方波输出
- 功能2：测量输入脉冲宽度
- 功能3：产生单次延时
- 通过按键切换功能

写出程序设计思路和关键代码。

**解答**：
设计思路：
1. 使用状态机管理三种功能模式
2. T0用于方波生成和延时功能
3. T1用于脉冲宽度测量
4. 外部中断检测按键切换模式

关键代码：
```c
#include <reg52.h>

#define MODE_SQUARE_WAVE  0
#define MODE_PULSE_WIDTH  1
#define MODE_DELAY        2

unsigned char current_mode = MODE_SQUARE_WAVE;
unsigned int pulse_width = 0;
bit delay_flag = 0;

void Mode_Switch() {
    current_mode++;
    if(current_mode > MODE_DELAY) {
        current_mode = MODE_SQUARE_WAVE;
    }

    // 重新初始化定时器
    TR0 = 0;
    TR1 = 0;

    switch(current_mode) {
        case MODE_SQUARE_WAVE:
            // 1KHz方波：500μs翻转
            TMOD = 0x01;
            TH0 = 0xFE;
            TL0 = 0x0C;
            TR0 = 1;
            break;

        case MODE_PULSE_WIDTH:
            // 脉宽测量模式
            TMOD = 0x10;
            IT0 = 1;      // 边沿触发
            EX0 = 1;      // 外部中断0
            break;

        case MODE_DELAY:
            // 延时模式
            TMOD = 0x01;
            // 根据需要设置延时时间
            break;
    }
}

void Timer0_ISR() interrupt 1 {
    if(current_mode == MODE_SQUARE_WAVE) {
        TH0 = 0xFE;
        TL0 = 0x0C;
        P1_0 = ~P1_0;     // 方波输出翻转
    } else if(current_mode == MODE_DELAY) {
        TR0 = 0;          // 停止定时器
        delay_flag = 1;   // 延时完成标志
    }
}

void External0_ISR() interrupt 0 {
    if(current_mode == MODE_PULSE_WIDTH) {
        if(P3_2 == 1) {   // 上升沿，开始测量
            TH1 = 0;
            TL1 = 0;
            TR1 = 1;
        } else {          // 下降沿，结束测量
            TR1 = 0;
            pulse_width = TH1 * 256 + TL1;
        }
    }
}

void main() {
    EA = 1;
    Mode_Switch();        // 初始化为方波模式

    while(1) {
        // 主循环处理各种功能
        if(delay_flag) {
            delay_flag = 0;
            // 延时完成处理
        }
    }
}
```

**知识点**：多功能定时器系统设计
**难度**：★★★★★
**答题时间**：20分钟

---

*练习题集第二部分完成，包含8259A中断控制器和8051定时器编程共19道题目。涵盖了从基础概念到综合应用的各个层次，为学生提供了全面的练习机会。*
