#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本提取工具
用于提取微机原理及单片机应用期末复习PDF文件的内容
"""

import PyPDF2
import sys
import os

def extract_pdf_text(pdf_path, output_path):
    """
    从PDF文件中提取文本内容
    
    Args:
        pdf_path (str): PDF文件路径
        output_path (str): 输出文本文件路径
    
    Returns:
        bool: 提取是否成功
    """
    try:
        # 检查PDF文件是否存在
        if not os.path.exists(pdf_path):
            print(f"错误：PDF文件不存在 - {pdf_path}")
            return False
        
        # 打开PDF文件
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            # 获取PDF信息
            num_pages = len(pdf_reader.pages)
            print(f"PDF文件信息：")
            print(f"- 文件路径：{pdf_path}")
            print(f"- 总页数：{num_pages}")
            
            # 提取所有页面的文本
            all_text = ""
            for page_num in range(num_pages):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                all_text += f"\n\n=== 第 {page_num + 1} 页 ===\n\n"
                all_text += text
                print(f"已处理第 {page_num + 1} 页")
            
            # 保存提取的文本
            with open(output_path, 'w', encoding='utf-8') as output_file:
                output_file.write(all_text)
            
            print(f"\n提取完成！")
            print(f"- 提取的文本已保存到：{output_path}")
            print(f"- 总字符数：{len(all_text)}")
            
            return True
            
    except Exception as e:
        print(f"提取PDF时发生错误：{str(e)}")
        return False

def main():
    # PDF文件路径
    pdf_file = "微机原理及单片机应用 期末复习-S.pdf"
    output_file = "学校知识点提取.txt"
    
    print("开始提取PDF文件内容...")
    print("=" * 50)
    
    # 提取PDF内容
    success = extract_pdf_text(pdf_file, output_file)
    
    if success:
        print("\n" + "=" * 50)
        print("PDF内容提取成功！")
        
        # 显示提取内容的前500个字符作为预览
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
                preview = content[:500] + "..." if len(content) > 500 else content
                print(f"\n内容预览：\n{preview}")
        except Exception as e:
            print(f"读取输出文件时出错：{str(e)}")
    else:
        print("\nPDF内容提取失败！")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
